import { describe } from 'node:test'
import React, { useState } from 'react'
import CheckmarkAnimation from '../animations/CheckmarkAnimations'

interface InputOnOffProps {
    title: string
    description?: string
    value: boolean
    onChange: (value: boolean) => void
}

function InputOnOff({ title, description, value, onChange }: InputOnOffProps) {

    const [isAnimation, setIsAnimation ] = useState(false)

    return (
        <div className='px-4 py-4 flex flex-row gap-4 items-center justify-between my-1 bg-[white] rounded-[20px]' style={{ height: 'auto', position: 'relative' }}>
            <div className='flex flex-col gap-1'>
                <p style={{ fontSize: '13px', fontWeight: '600' }}>{title}</p>
                {
                    description &&
                    <p style={{ fontSize: '11px', fontWeight: '400', color: '#00000090' }}>{description}</p>
                }
            </div>
            {
                isAnimation &&
                <div style={{ position: 'absolute', top: '-17px', left: '-50px' }}>
                    <CheckmarkAnimation 
                        onComplete={()=>{
                            setIsAnimation(false)
                        }}
                    />
                </div>
            }



            <div className='flex flex-row gap-2'>
                <button
                    onClick={() => {
                        onChange(true)
                        setIsAnimation(true)
                    }}
                    style={{
                        width: '70px',
                        height: '35px',
                        borderRadius: '10px',
                        backgroundColor: !value ? '#E5E5E5' : 'var(--blue)',
                        color: !value ? 'var(--blue)' : '#E5E5E5',
                        fontSize: '13px'
                    }}
                    className='flex items-center justify-center'
                >
                    Si
                </button>

                <button
                    onClick={() => {
                        onChange(false)
                        setIsAnimation(true)
                    }}
                    style={{
                        width: '70px',
                        height: '35px',
                        borderRadius: '10px',
                        backgroundColor: value ? '#E5E5E5' : 'var(--blue)',
                        color: value ? 'var(--blue)' : '#E5E5E5',
                        fontSize: '13px'

                    }}
                    className='flex items-center justify-center'
                >
                    No
                </button>
            </div>
        </div>
    )
}

export default InputOnOff