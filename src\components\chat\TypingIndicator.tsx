import React from 'react';
import { TypingStatus } from '@/types/support';

interface TypingIndicatorProps {
  typingStatus: TypingStatus;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({ 
  typingStatus, 
  className = '' 
}) => {
  if (!typingStatus.isTyping || typingStatus.userIds.length === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 p-3 ${className}`}>
      <div className="flex space-x-1">
        <div className="typing-dots">
          <div className="dot1"></div>
          <div className="dot2"></div>
          <div className="dot3"></div>
        </div>
      </div>
      <span className="text-sm text-gray-500">Assistenza affitti sta scrivendo...</span>
      
      <style jsx>{`
        .typing-dots {
          display: flex;
          align-items: center;
          gap: 2px;
        }
        
        .typing-dots > div {
          width: 4px;
          height: 4px;
          background-color: #9ca3af;
          border-radius: 50%;
          animation: typing 1.4s infinite ease-in-out;
        }
        
        .dot1 {
          animation-delay: -0.32s;
        }
        
        .dot2 {
          animation-delay: -0.16s;
        }
        
        @keyframes typing {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};