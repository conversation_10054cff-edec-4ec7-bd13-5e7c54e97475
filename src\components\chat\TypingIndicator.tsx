import React from 'react';
import { TypingStatus } from '@/types/support';

interface TypingIndicatorProps {
  typingStatus: TypingStatus;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  typingStatus,
  className = ''
}) => {
  if (!typingStatus.isTyping || typingStatus.userIds.length === 0) {
    return null;
  }

  return (
    <div className={`flex justify-start mb-3 ${className}`}>
      <div
        style={{
          maxWidth: '70%',
          padding: '10px 20px',
          borderRadius: '0 20px 20px 20px',
          boxShadow: '0px 0px 10px 0px #00000010',
          backgroundColor: 'white',
          border: '1px solid #e5e7eb'
        }}
      >
        <div className="flex items-center space-x-2">
          <div className="typing-dots">
            <div className="dot1"></div>
            <div className="dot2"></div>
            <div className="dot3"></div>
          </div>
          <span className="text-sm text-gray-500">Assistenza affitti sta scrivendo...</span>
        </div>
      </div>

      <style jsx>{`
        .typing-dots {
          display: flex;
          align-items: center;
          gap: 3px;
          margin-right: 8px;
        }

        .typing-dots > div {
          width: 6px;
          height: 6px;
          background-color: #6b7280;
          border-radius: 50%;
          animation: typing 1.4s infinite ease-in-out;
        }

        .dot1 {
          animation-delay: -0.32s;
        }

        .dot2 {
          animation-delay: -0.16s;
        }

        .dot3 {
          animation-delay: 0s;
        }

        @keyframes typing {
          0%, 80%, 100% {
            transform: scale(0.6);
            opacity: 0.4;
          }
          40% {
            transform: scale(1.2);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};