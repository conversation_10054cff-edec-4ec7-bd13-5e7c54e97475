'use client'
import React, { useEffect, useState } from 'react'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import MultiSelect from '@/components/inputs/multiSelect'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import CardAlert from '@/components/cards/CardAlert'

import { collaboratorPermissionsOptions } from '@/models/modelCollaboratorPermissions'
import ButtonLoading from '@/components/buttons/ButtonLoading'
import { removeCollaborator, updateCollaborator, viewCollaborators } from '@/services/api'
import toast from 'react-hot-toast'



interface PageProps {
  params: {
    propertyId: string,
    userId: string,
  }
}


function Page({ params }: PageProps) {
  const router = useRouter()

  
  const [selectedOptions, setSelectedOptions] = useState<any>([])
  const [collaboratorData, setCollaboratorData] = useState<any>(null)


  const handleUpdateCollaborator = async () => {
    const call = await updateCollaborator(params.propertyId, params.userId, selectedOptions)

    if (call.status === 200 || call.status === 201){
      toast.success(`Permessi ${collaboratorData.name} cambiati con successo`)
    } else {
      toast.error('Problema nel salvataggio dei permessi')
    }

    return true
  }
  const handleRemoveCollaborator = async () => {
    const call = await removeCollaborator(params.propertyId, params.userId)

    toast.success(`Permessi rimossi con successo a ${collaboratorData.name}`)

    setTimeout(()=>{
      router.back()
      return true
    }, 500)
  }

  const handleFetchCollaborators = async () => {
    const call = await viewCollaborators(params.propertyId)

    if (call.status === 200) {
      const collaborator = call.data.find((collab: any) => collab.user_id == params.userId)
      setCollaboratorData(collaborator)
      setSelectedOptions(collaborator.permissions)
    }
  }


  useEffect(() => {
    handleFetchCollaborators()
  }, [])

  return (
    <MobilePageStart>
      <HeaderPage title='Permessi'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      {
        collaboratorData &&
        <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>


          <div className='flex flex-col gap-4'>
            <CardAlert
              title='Aggiornamento Permesso'
              message="Modifica i permessi che questo collaboratore ha all'interno di questa proprietà, oppure rimuovilo"
              color='orange'
            />

            <InputLabelDescription
              label='Aggiungi Collaboratore'
              placeholder='E-mail del collaboratore'
              value={collaboratorData.email}
              onChange={()=>{}}
              
            />

            <MultiSelect
              options={collaboratorPermissionsOptions}
              onChange={(valueId) => { setSelectedOptions(valueId) }}
              value={selectedOptions}
              title='Quali permessi deve avere il collaboratore?'
              isMultiSelect

              optionsStyle={{
                fontSizeDescription: '10px',
                fontSizeTitle: '12px'
              }}
              unselectingAnswer={1}
            />

            <div className='flex flex-row gap-2'>
              <ButtonLoading
                text={'Salva'}
                onClick={() => {
                  return handleUpdateCollaborator()
                 }}
                color='white'
                backgroundColor='#00000080'
              />
              <ButtonLoading
                text={'Rimuovi Collaboratore'}
                onClick={() => { 
                  return handleRemoveCollaborator()
                }}
                color='white'
                backgroundColor='var(--blue)'
              />
            </div>






          </div>



        </div>
      }


    </MobilePageStart>
  )
}

export default Page