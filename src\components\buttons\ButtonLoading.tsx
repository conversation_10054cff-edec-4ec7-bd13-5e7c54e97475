'use client'

import dynamic from 'next/dynamic'
import React, { useState } from 'react'
import style from './button.module.css'

const LoaderCircle = dynamic(() => import('../loaders/LoaderCircle'), {
  ssr: false
})

interface ButtonProps {
    onClick: () => any
    color: string
    backgroundColor: string
    text: any
    deactive?: boolean
    isLoading?: boolean
}

function ButtonLoading({ onClick, color, backgroundColor, text, deactive }: ButtonProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleClick = async () => {
    if (isLoading) return
    setIsLoading(true)

    const finish = await onClick()
    
    if (finish) {
      setIsLoading(false)
    } else {
      setIsLoading(false)
    }

  }
  

  return (
    <div
      className={style.component}
      style={{
        background: backgroundColor,
        color: color,
        cursor: isLoading || deactive ? 'not-allowed' : 'pointer',
        pointerEvents: isLoading || deactive ? 'none' : 'auto', // Fully disables click during loading
        opacity: isLoading || deactive ? 0.5 : 1,
        marginBottom: '20px'
      }}
      onClick={handleClick}
    >
      {isLoading ? <LoaderCircle /> : text}
    </div>
  )
}

export default ButtonLoading
