'use client'
import React, { useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import { useRouter } from 'next/navigation'
import HeaderPage from '@/components/_globals/headerPage'
import { Calendar, Clock, CreditCard, Home, MapPin, MessageSquare, Phone, User, Users, Laptop, ClipboardList, XCircle } from 'lucide-react'
import BookingCancellationModal from '@/components/modals/BookingCancellationModal'

const statusConfig = {
  "new": { color: '#2196F3', bgColor: '#E3F2FD', icon: 'sparkles', label: 'Nuova' },
  "modified": { color: '#FF9800', bgColor: '#FFF3E0', icon: 'pencil', label: 'Modificata' },
  "request": { color: '#9C27B0', bgColor: '#F3E5F5', icon: 'clock', label: '<PERSON><PERSON>' },
  "cancelled": { color: '#F44336', bgColor: '#FFEBEE', icon: 'x', label: 'Cancellata' },
  "completed": { color: '#4CAF50', bgColor: '#E8F5E9', icon: 'check', label: 'Completata' }
} as const;

interface BookingDataType {
  id: string;
  is_manual: boolean;
  status: keyof typeof statusConfig;
  customer: {
    first_name: string;
    last_name: string;
    email: string;
    telephone: string;
    address: string;
    city: string;
    state: string;
    country: string;
    zip_code: string;
  };
  reservation_data: {
    checkin_date: string;
    checkout_date: string;
    total_price: string;
    gross_price: string;
    total_tax: string;
    deposit: string;
    payment_type: string;
    number_of_adults: number;
    number_of_children: number;
    number_of_infants: number;
    booked_at: string;
    modified_at: string;
    remarks?: string;
    addons?: Array<{
      name: string;
      price: string;
      tax: string;
      nights: string;
    }>;
    commission_amount: string;
  };
  property: {
    name: string;
  };
}

const Page = () => {
  const [bookingData, setBookingData] = useState<BookingDataType | null>(null)
  const [showCancellationModal, setShowCancellationModal] = useState(false)
  const router = useRouter()

  const getBookingDetails = () => {
    const booking = localStorage.getItem('selectedBooking-heibooky')
    if (booking) {
      return JSON.parse(booking)
    }
    return null
  }

  function calculateNights(checkinDate: string, checkoutDate: string): number {
    const checkin = new Date(checkinDate);
    const checkout = new Date(checkoutDate);
    const timeDifference = checkout.getTime() - checkin.getTime();
    const millisecondsInOneDay = 24 * 60 * 60 * 1000;
    return timeDifference / millisecondsInOneDay;
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  useEffect(() => {
    const data = getBookingDetails()
    if (data) {
      setBookingData(data)
    }
  }, [])

  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'new':
        return <div className="w-5 h-5 text-white">✨</div>;
      case 'modified':
        return <div className="w-5 h-5 text-white">✏️</div>;
      case 'request':
        return <Clock className="w-5 h-5 text-white" />;
      case 'cancelled':
        return <div className="w-5 h-5 text-white">❌</div>;
      case 'completed':
        return <div className="w-5 h-5 text-white">✓</div>;
      default:
        return null;
    }
  };

  const getTotalWithAddons = (totalPrice: string, addons: any[] | undefined) => {
    let total = parseFloat(totalPrice);
    if (addons && addons.length > 0) {
      addons.forEach(addon => {
        total += parseFloat(addon.price);
      });
    }
    return total.toFixed(2);
  };

  // Helper function to safely get status config
  const getStatusConfig = (status: string) => {
    if (status && statusConfig[status as keyof typeof statusConfig]) {
      return statusConfig[status as keyof typeof statusConfig];
    }
    // Return default if status is invalid
    return { color: '#999', bgColor: '#f0f0f0', icon: 'circle', label: 'Sconosciuto' };
  };

  return (
    <MobilePageStart isNavbar customPaddingBottom='10px'>
      <HeaderPage
        title='Dettagli Prenotazione'
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push('/dashboard/bookings')}
      />
      <div className='w-full h-full flex flex-col p-3 gap-4 overflow-auto bg-gray-50'>
        {bookingData && (
          <>
            {/* Status and Booking ID Banner */}
            <div className='bg-white rounded-lg p-4 shadow-sm border-l-4' style={{ borderLeftColor: '#133157' }}>
              <div className='flex justify-between items-center'>
                <div className='flex flex-col'>
                  <span className='text-sm text-gray-500'>Prenotazione ID</span>
                  <span className='font-bold text-lg text-[#133157]'>#{bookingData.id.slice(0,8)}</span>
                </div>
                <div
                  className='flex items-center gap-2 px-4 py-2 rounded-full text-white'
                  style={{ backgroundColor: getStatusConfig(bookingData.status).color }}
                >
                  {renderStatusIcon(bookingData.status)}
                  <span className='font-medium'>{getStatusConfig(bookingData.status).label}</span>
                </div>
              </div>
            </div>
            <div className='flex items-center gap-2'>
              {bookingData.is_manual ? (
                <div className='flex items-center gap-1 px-2 py-0.5 bg-gray-100 rounded-full text-xs text-gray-600'>
                  <ClipboardList className='w-3 h-3' />
                  <span>Inserita Manualmente</span>
                </div>
              ) : (
                <div className='flex items-center gap-1 px-2 py-0.5 bg-blue-50 rounded-full text-xs text-blue-600'>
                  <Laptop className='w-3 h-3' />
                  <span>Da Channel Manager</span>
                </div>
              )}
            </div>
            {/* Property Card with Check-in/out */}
            <div className='bg-white rounded-lg overflow-hidden shadow-sm'>
              <div className='bg-[#133157] text-white p-4'>
                <div className='flex justify-between items-center'>
                  <div className='flex items-center gap-2'>
                    <Home className='w-5 h-5' />
                    <h3 className='font-bold'>{bookingData.property.name}</h3>
                  </div>
                  <div className='bg-[#febd49] text-[#133157] px-3 py-1 rounded-full text-sm font-bold'>
                    {calculateNights(bookingData.reservation_data.checkin_date, bookingData.reservation_data.checkout_date)} Notti
                  </div>
                </div>
              </div>
              <div className='p-4'>
                <div className='grid grid-cols-2 gap-4'>
                  <div className='border-r border-gray-200 pr-4'>
                    <div className='flex items-center gap-2 mb-1'>
                      <Calendar className='w-4 h-4 text-[#133157]' />
                      <span className='text-gray-500 text-sm'>Check-in</span>
                    </div>
                    <p className='font-medium'>{formatDate(bookingData.reservation_data.checkin_date)}</p>
                    <p className='text-sm text-gray-500'>Dalle 15:00</p>
                  </div>
                  <div>
                    <div className='flex items-center gap-2 mb-1'>
                      <Calendar className='w-4 h-4 text-[#133157]' />
                      <span className='text-gray-500 text-sm'>Check-out</span>
                    </div>
                    <p className='font-medium'>{formatDate(bookingData.reservation_data.checkout_date)}</p>
                    <p className='text-sm text-gray-500'>Entro le 10:00</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Customer Details */}
            <div className='bg-white rounded-lg p-4 shadow-sm'>
              <div className='flex items-center gap-2 mb-3'>
                <User className='w-5 h-5 text-[#133157]' />
                <h3 className='font-bold text-lg'>Dettagli Cliente</h3>
              </div>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex flex-col'>
                  <label className='text-gray-500 text-sm'>Nome Completo</label>
                  <p className='font-medium'>{bookingData.customer.first_name} {bookingData.customer.last_name}</p>
                </div>
                <div className='flex flex-col'>
                  <div className='flex items-center gap-1'>
                    <Phone className='w-4 h-4 text-[#133157]' />
                    <label className='text-gray-500 text-sm'>Telefono</label>
                  </div>
                  <p>{bookingData.customer.telephone}</p>
                </div>
                <div className='flex flex-col'>
                  <label className='text-gray-500 text-sm'>Email</label>
                  <p className='text-[#133157]'>{bookingData.customer.email}</p>
                </div>
                <div className='flex flex-col'>
                  <div className='flex items-center gap-1'>
                    <MapPin className='w-4 h-4 text-[#133157]' />
                    <label className='text-gray-500 text-sm'>Indirizzo</label>
                  </div>
                  <p>{bookingData.customer.address}, {bookingData.customer.city}</p>
                  <p>{bookingData.customer.country} {bookingData.customer.zip_code}</p>
                </div>
              </div>
            </div>

            {/* Stay Details */}
            <div className='bg-white rounded-lg p-4 shadow-sm'>
              <div className='flex items-center gap-2 mb-3'>
                <Users className='w-5 h-5 text-[#133157]' />
                <h3 className='font-bold text-lg'>Dettagli Soggiorno</h3>
              </div>
              <div className='grid grid-cols-2 gap-4'>
                <div className='bg-gray-50 p-3 rounded-lg'>
                  <label className='text-gray-500 text-sm'>Ospiti</label>
                  <div className='flex gap-2 mt-1'>
                    <span className='bg-[#133157] text-white px-2 py-1 rounded text-sm'>
                      {bookingData.reservation_data.number_of_adults} Adulti
                    </span>
                    {bookingData.reservation_data.number_of_children > 0 && (
                      <span className='bg-[#febd49] text-[#133157] px-2 py-1 rounded text-sm'>
                        {bookingData.reservation_data.number_of_children} Bambini
                      </span>
                    )}
                    {bookingData.reservation_data.number_of_infants > 0 && (
                      <span className='bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm'>
                        {bookingData.reservation_data.number_of_infants} Neonati
                      </span>
                    )}
                  </div>
                </div>
                <div className='bg-gray-50 p-3 rounded-lg'>
                  <label className='text-gray-500 text-sm'>Prenotazione</label>
                  <p className='text-sm mt-1'>
                    Effettuata il {formatDate(bookingData.reservation_data.booked_at)}<br/>
                    {bookingData.status === 'modified' && `Modificata il ${formatDate(bookingData.reservation_data.modified_at)}`}
                  </p>
                </div>
              </div>
            </div>

            {/* Payment Details */}
            <div className='bg-white rounded-lg p-4 shadow-sm mb-4'>
              <div className='flex items-center gap-2 mb-3'>
                <CreditCard className='w-5 h-5 text-[#133157]' />
                <h3 className='font-bold text-lg'>Dettagli Pagamento</h3>
              </div>

              <div className='bg-gray-50 p-3 rounded-lg mb-4'>
                <div className='flex justify-between items-center'>
                  <span className='text-gray-500'>Tipo Pagamento</span>
                  <span className='font-medium'>{bookingData.reservation_data.payment_type}</span>
                </div>
              </div>

              <div className='space-y-2 mb-4'>
                <div className='flex justify-between'>
                  <span className='text-gray-500'>Prezzo Base</span>
                  <span>€{bookingData.reservation_data.gross_price}</span>
                </div>

                <div className='flex justify-between'>
                  <span className='text-gray-500'>Tasse</span>
                  <span>€{bookingData.reservation_data.total_tax || '0'}</span>
                </div>

                {bookingData.reservation_data.addons && bookingData.reservation_data.addons.length > 0 && (
                  <>
                    <div className='border-t border-gray-200 pt-2'>
                      <p className='text-gray-500 mb-1'>Extra:</p>
                      {bookingData.reservation_data.addons.map((addon, index) => (
                        <div key={index} className='flex justify-between'>
                          <span>{addon.name} ({addon.nights} notti)</span>
                          <span>€{addon.price}</span>
                        </div>
                      ))}
                    </div>
                  </>
                )}

                {parseFloat(bookingData.reservation_data.deposit) > 0 && (
                  <div className='flex justify-between'>
                    <span className='text-gray-500'>Deposito</span>
                    <span>€{bookingData.reservation_data.deposit}</span>
                  </div>
                )}

                <div className='flex justify-between border-t border-gray-200 pt-2 mt-2'>
                  <span className='text-gray-500'>Commissione</span>
                  <span>€{bookingData.reservation_data.commission_amount}</span>
                </div>
              </div>

              <div className='bg-[#133157] text-white p-3 rounded-lg flex justify-between items-center'>
                <span className='font-bold'>Totale Finale</span>
                <span className='font-bold text-xl'>
                  €{getTotalWithAddons(bookingData.reservation_data.total_price, bookingData.reservation_data.addons)}
                </span>
              </div>
            </div>

            {/* Notes Section */}
            {bookingData.reservation_data.remarks ? (
              <div className='bg-white rounded-lg p-4 shadow-sm mb-4'>
                <div className='flex items-center gap-2 mb-3'>
                  <MessageSquare className='w-5 h-5 text-[#133157]' />
                  <h3 className='font-bold text-lg'>Note e Richieste</h3>
                </div>
                <div className='bg-[#febd49] bg-opacity-10 p-3 rounded-lg border-l-4 border-[#febd49]'>
                  <p>{bookingData.reservation_data.remarks}</p>
                </div>
              </div>
            ) : (
              <div className='mb-4'></div>
            )}

            {/* Cancellation Button */}
            {bookingData.status !== 'cancelled' && bookingData.status !== 'completed' &&
            (
              <button
                onClick={() => setShowCancellationModal(true)}
                className='w-full bg-red-500 hover:bg-red-600 text-white py-3 px-4 rounded-lg shadow-sm flex items-center justify-center gap-2 transition-colors mb-4'
              >
                <XCircle className='w-5 h-5' />
                <span className='font-medium'>Richiedi Cancellazione</span>
              </button>
            )}
          </>
        )}
      </div>

      {/* Cancellation Modal */}
      {bookingData && (
        <BookingCancellationModal
          isOpen={showCancellationModal}
          onClose={() => setShowCancellationModal(false)}
          bookingId={bookingData.id}
        />
      )}
    </MobilePageStart>
  )
}

export default Page