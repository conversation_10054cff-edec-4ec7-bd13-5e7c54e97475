'use client'
import React, { useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { useRouter } from 'next/navigation'
import { getInvoices } from '@/services/api'
import Loader1 from '@/components/loaders/Loader1'
import Card from '@/components/cards/Card'
import CardAlert from '@/components/cards/CardAlert'


function Page() {
  const router = useRouter()


  const [invoices, setInvoices] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  const handleFetchInvoices = async () => {
    const call = await getInvoices()

    if (call.status === 200 || call.status === 201) {
      setInvoices(call.data)
    }

    setIsLoading(false)
  }
  const handleDownloadPdf = (downloadUrl: string) => {
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.target = '_blank'; // opens in a new tab
    let fileName = 'invoice.pdf';
    try {
      const urlObj = new URL(downloadUrl);
      fileName = urlObj.pathname.split('/').pop() || fileName;
    } catch (e) {
      console.error('Error parsing download URL:', e);
    }
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  

  useEffect(() => {
    handleFetchInvoices()
  }, [])




  return (
    <MobilePageStart>
      <HeaderPage title='Fatture'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>

        {
          isLoading &&
          <div className='w-full h-full flex items-center justify-center'>
            <Loader1 />
          </div>
        }

        {
          !isLoading &&
          invoices.length > 0 &&
          <CardAlert
            color='green'
            title='Ricevute'
            message='In questa lista, sono visibile le ricevute generate per il tuo account e le tue proprietà'
          />
        }
        {
          !isLoading &&
          invoices.length > 0 &&
          invoices.map((invoice: InvoiceModelProps) => {
            return (
              <Card
                key={invoice.id}
                style={{ width: '100%', padding: '10px 20px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row' }}
                onClick={() => {
                  handleDownloadPdf(invoice.pdf_file)
                }}
              >

                <p>{invoice.progressive_number}.pdf</p>
                <div className='bg-[var(--blue)] py-2 px-6  text-white text-[14px] rounded-lg'>Download</div>
              </Card>

            )
          })
        }
        {
          !isLoading &&
          invoices.length == 0 &&
          <div className='flex flex-col items-center h-[400px] justify-center'>
            <p className='text-[23px] text-[var(--blue)]'>Nessuna Fattura</p>
            <p className='text-[13px] text-[var(--blue)]'>Torna quando saranno state generate</p>
          </div>
        }




      </div>
    </MobilePageStart>
  )
}

export default Page