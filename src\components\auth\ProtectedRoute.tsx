'use client';

import { useContext, useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { AuthContext } from '@/components/_context/AuthContext';
import Loader1 from '@/components/loaders/Loader1';

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: string[]; // Optional roles for role-based access control
}

/**
 * Component to protect routes that require authentication
 * Redirects to login if user is not authenticated
 */
const ProtectedRoute = ({ children, roles }: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading, checkAuth } = useContext(AuthContext);
  const router = useRouter();
  const pathname = usePathname();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const verifyAuth = async () => {
      try {
        // Check authentication status directly
        // This will internally handle token refresh if needed
        const isValid = await checkAuth();

        if (!isValid) {
          // Redirect to login with return path
          const redirectPath = encodeURIComponent(pathname);
          router.push(`/auth/login?redirect=${redirectPath}`);
        }
      } catch (error) {
        console.error('Authentication verification failed:', error);
        // Redirect to login on error
        const redirectPath = encodeURIComponent(pathname);
        router.push(`/auth/login?redirect=${redirectPath}`);
      } finally {
        setIsChecking(false);
      }
    };

    verifyAuth();
  }, [checkAuth, pathname, router]);

  // Show loading state while checking authentication
  if (isLoading || isChecking) {
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <Loader1 />
      </div>
    );
  }

  // If authenticated, render children
  return isAuthenticated ? <>{children}</> : null;
};

export default ProtectedRoute;
