'use client'
import React, { useEffect, useState } from 'react'
import MobilePage from '@/components/_globals/mobilePage'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import MultiSelect from '@/components/inputs/multiSelect'
import Button from '@/components/buttons/Button'
import InputCounter from '@/components/inputs/inputCounter'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import Link from 'next/link'
import ButtonSection from '@/components/buttons/ButtonSection'


function Page() {
  const router = useRouter()

  const [selected, setSelected] = useState(['1'])



  return (
    <MobilePageStart>
      <HeaderPage title='Account Policy'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard/settings`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-4'>

      

        <MultiSelect
          isMultiSelect={false}
          title='Per quale motivo vuoi cancellare il tuo account?'
          value={selected}
          onChange={(selected) => {
            setSelected(selected)
          }}
          options={[
            {
              id: '1',
              title: 'Non mi piace il servizio',
            },
            {
              id: '2',
              title: 'Non mi trovo bene',
            },
            {
              id: '3',
              title: 'Ho trovato una alternativa migliore',
            },
            {
              id: '5',
              title: 'Motivi di Privacy',
            },
            {
              id: '4',
              title: 'Altro',
            }
          ]}
        />

        <Button
          text='Cancel Account'
          onClick={() => {}}
          color='white'
          backgroundColor='tomato'
        />


      </div>


    </MobilePageStart>
  )
}

export default Page