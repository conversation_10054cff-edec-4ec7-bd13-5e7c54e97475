/**
 * Utility functions for handling API error responses
 */

/**
 * Format error messages from a structured API error response
 * 
 * @param error The error object from the API
 * @returns A formatted error message string
 * 
 * Example error structure:
 * {
 *   "errors": {
 *     "email": ["A user with that email already exists."],
 *     "phone": ["The phone number entered is not valid."]
 *   }
 * }
 */
export const formatErrorMessage = (error: any): string => {
  // If error is a string, return it directly
  if (typeof error === 'string') {
    return error;
  }

  // If error has a detail property, return it
  if (error?.detail) {
    return error.detail;
  }

  // If error has a message property, return it
  if (error?.message) {
    return error.message;
  }

  // Handle structured errors with field-specific messages
  if (error?.errors && typeof error.errors === 'object') {
    const errorMessages: string[] = [];
    
    // Iterate through each field in the errors object
    Object.entries(error.errors).forEach(([field, messages]) => {
      // Handle array of messages for each field
      if (Array.isArray(messages)) {
        messages.forEach(message => {
          errorMessages.push(`${field}: ${message}`);
        });
      } 
      // Handle single message as string
      else if (typeof messages === 'string') {
        errorMessages.push(`${field}: ${messages}`);
      }
    });
    
    // Join all error messages with line breaks
    if (errorMessages.length > 0) {
      return errorMessages.join('\n');
    }
  }

  // Default error message if we can't parse the error
  return 'Si è verificato un errore. Riprova più tardi.';
};

/**
 * Display a toast notification for an API error
 * 
 * @param error The error object from the API
 * @param toast The toast function to use
 * @param defaultMessage Optional default message if error can't be parsed
 */
export const handleApiError = (error: any, toast: any, defaultMessage = 'Si è verificato un errore. Riprova più tardi.') => {
  const errorMessage = formatErrorMessage(error) || defaultMessage;
  toast.error(errorMessage);
};
