.card {
    width: 710px;
    padding: 50px 30px;

    background-color: var(--card-background);
    display: flex;
    flex-direction: column;

    box-shadow:
        0 0 0 1px hsla(0, 0%, 100%, 0.08),
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 12px 24px rgba(0, 0, 0, 0.3);

    align-items: center;
    justify-content: center;

    border-radius: 20px;
}


.containerCardAlert {
	height: auto;
	width: 100%;



	.cardAlert {
		display: flex;
		flex-direction: row;
		padding: 15px;
		border: 1px solid #b2723695;
		border-radius: 10px;

		.iconContainer {
			height: 100%;
			display: flex;
			align-items: flex-start;
			margin-right: 15px;
			.icon {
				width: 20px;
			}
		}

		.info {
			width: 100%;
			padding-right: 20px;

			display: flex;
			flex-direction: column;
			align-items: flex-start;
			justify-content: center;
			.title {
				color: rgba(0, 0, 0, 0.731);
				font-size: 14px;
				font-weight: 700;
				margin-bottom: 5px;
			}

			.message {
				color: rgba(0, 0, 0, 0.423);
				font-size: 12px;
				line-height: 18px;
			}

			.link {
				color: #000000;
				text-decoration: underline;
			}
		}

		.button {
			transform: translateY(calc(50% - 12px));
			width: 150px;
			height: 27px;
			border-radius: 7px;
			background-color: rgb(147, 156, 250);
			font-size: 13px;
			cursor: pointer;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}

.creditCard{
	width: 480px;
	height: 300px;
	background: linear-gradient(120deg, #48639786, #2e2e2e);
	border-radius: 20px;
	padding: 20px;

	display: flex;
	flex-direction: column;
	justify-content: space-between;
	position: relative;

	.chip{
		position: absolute;
		width: 80px;
		top: 35%;
	}
	.paymentProvider{
		width: 80px;
	}
}



/* Card Property */

.cardPropertyImageWrapper {
	width: 100%;
	height: 200px;
	overflow: hidden;
	border-radius: 10px 10px 0 0;
}

.cardPropertyImage{
	width: 100%;
	height: 200px;
	object-fit: cover;
	border-radius: 10px 10px 0 0;
}

.cardPropertyContent{
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 100%;
	height: 60px;
	padding: 0 10px;
	position: relative;

	.name{
		font-size: 14px;
		color: rgba(0, 0, 0, 0.731);
		font-weight: 700;
	}

	.id{
		font-size: 12px;
		color: rgba(0, 0, 0, 0.423);
	}

	.badge {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		background-color: var(--accent);
		color: white;
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 12px;
	}

	.statusIndicator {
		position: absolute;
		right: 10px;
		top: 50%;
		transform: translateY(-50%);
		width: 8px;
		height: 8px;
		border-radius: 50%;
		transition: background-color 0.2s ease;
	}

	.statusIndicator.active {
		background-color: #22c55e;
		box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.2);
	}

	.statusIndicator.inactive {
		background-color: #ef4444;
		box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);
	}

	.statusIndicator.deactivated {
		background-color: #6b7280;
		box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% {
			box-shadow: 0 0 0 0 rgba(107, 114, 128, 0.4);
		}
		70% {
			box-shadow: 0 0 0 6px rgba(107, 114, 128, 0);
		}
		100% {
			box-shadow: 0 0 0 0 rgba(107, 114, 128, 0);
		}
	}
}