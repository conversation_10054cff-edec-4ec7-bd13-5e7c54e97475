import React, { useState, useContext } from 'react';
import Card from './Card';
import styles from './card.module.css';
import Link from 'next/link';
import ImageLoader from '../loaders/ImageLoader';
import { UserContext } from '@/components/_context/UserContext';
import { showNavbarSafeToast } from '@/components/toast/NavbarSafeToast';

interface CardPropertyProps {
    name: string;
    id: string;
    hotel_id: string;
    picture: any[];
    is_onboarded: boolean;
    is_active?: boolean;
    cover_image?: string;
}

function CardProperty({ name, id, hotel_id, picture, cover_image, is_onboarded, is_active = true }: CardPropertyProps) {
    const [isLoading, setIsLoading] = useState(true);
    const { staffProperties } = useContext(UserContext);

    // Check if the user has Cleaning Staff permission for this property
    const isCleaningStaff = staffProperties.some(
        (staffProperty) =>
            staffProperty.property_id === id &&
            staffProperty.permissions.includes('Cleaning Staff')
    );

    const imageUrl = cover_image
        ? cover_image
        : picture.length > 0
            ? picture[0].image
            : 'https://news.airbnb.com/wp-content/uploads/sites/4/2023/04/Private-Room-Cape-Town.jpeg?w=512';

    // Determine if the property is disabled (cleaning staff or inactive)
    const isDisabled = isCleaningStaff || !is_active;

    const handlePropertyClick = (e: React.MouseEvent) => {
        if (isCleaningStaff) {
            e.preventDefault();
            showNavbarSafeToast({
                message: 'Non hai il permesso di modificare questa proprietà',
                type: 'error'
            });
        } else if (!is_active) {
            e.preventDefault();
            showNavbarSafeToast({
                message: 'Questa proprietà è stata disattivata e verrà eliminata dal sistema a breve. Contatta il supporto se desideri annullare la richiesta di disattivazione.',
                type: 'error',
                duration: 5000, // Show for 5 seconds
                style: {
                    maxWidth: '500px',
                    padding: '16px'
                }
            });
        }
    };

    return (
        <Card style={{ width: '100%', padding: '0', borderRadius: '10px' }}>
            <Link
                className="w-full h-full"
                href={`/dashboard/property/${id}`}
                onClick={handlePropertyClick}
                style={{
                    cursor: isDisabled ? 'not-allowed' : 'pointer',
                }}
            >
                <div
                    className={styles.cardPropertyImageWrapper}
                    style={{
                        opacity: isDisabled ? 0.5 : 1,
                        filter: isDisabled ? 'grayscale(100%)' : 'none',
                    }}
                >
                    {isLoading && <ImageLoader />}
                    <img
                        className={styles.cardPropertyImage}
                        src={imageUrl}
                        alt=""
                        onLoad={() => setIsLoading(false)}
                        onError={() => setIsLoading(false)} // Hide loading if the image fails to load
                        style={{ display: isLoading ? 'none' : 'block' }}
                    />
                </div>
                <div
                    className={styles.cardPropertyContent}
                    style={{
                        opacity: isDisabled ? 0.7 : 1,
                    }}
                >
                    <p className={styles.name}>{name}</p>
                    <p className={styles.id}>{hotel_id}</p>
                    <div className="flex items-center gap-1">
                        {!is_active ? (
                            <div className="flex items-center">
                                <div
                                    className={`${styles.statusIndicator} ${styles.deactivated}`}
                                    title="Disattivata"
                                />
                                <span className="text-xs text-gray-500 ml-3 mr-2">Disattivata</span>
                            </div>
                        ) : (
                            <div
                                className={`${styles.statusIndicator} ${is_onboarded ? styles.active : styles.inactive}`}
                                title={is_onboarded ? 'Sincronizzata' : 'Non sincronizzata'}
                            />
                        )}
                    </div>
                </div>
            </Link>
        </Card>
    );
}

export default CardProperty;
