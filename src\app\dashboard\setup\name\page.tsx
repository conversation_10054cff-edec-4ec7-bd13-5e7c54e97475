'use client'
import React, {  useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'

const NAME_MIN_LENGTH = 5;
const NAME_MAX_LENGTH = 50;
const DESCRIPTION_MIN_LENGTH = 20;
const DESCRIPTION_MAX_LENGTH = 100;

function Page() {
  const router = useRouter()

  const [name, setName] = useState('')
  const [description, setDescription] = useState('')

  const handleSaveAndContinue = () => {
    if (!name.trim()) {
      return toast.error('Il nome è obbligatorio')
    }

    if (name.length < NAME_MIN_LENGTH || name.length > NAME_MAX_LENGTH) {
      return toast.error(`Il nome deve essere tra ${NAME_MIN_LENGTH} e ${NAME_MAX_LENGTH} caratteri`)
    }

    if (description.length < DESCRIPTION_MIN_LENGTH || description.length > DESCRIPTION_MAX_LENGTH) {
      return toast.error(`La descrizione deve essere tra ${DESCRIPTION_MIN_LENGTH} e ${DESCRIPTION_MAX_LENGTH} caratteri`)
    }

    //Temporary - since there is no start setup page (no multiproperty or single selection) we skip that page and save single property automatically
    localStorage.setItem('propertyMode-crp', '1')
    
    //Save in localstorage
    localStorage.setItem('name-crp', name)
    localStorage.setItem('description-crp', description)

    //Redirect to next page
    router.push('/dashboard/setup/general')
  }


  return (
    <MobilePageStart>
      <HeaderPage title='Name'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push('/dashboard')
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col justify-between'>
        <div className='flex flex-col gap-8'>
          <InputLabelDescription
            label='Qual é il nome della tua proprietà?'
            value={name}
            onChange={(value) => {
              // Only allow alphanumeric, hyphen and apostrophe
              const sanitizedValue = value.replace(/[^a-zA-Z0-9\s'-]/g, '')
              setName(sanitizedValue)
            }}
            description='Perfavore non includere testi descrittivi quali: indirizzo, regole, ecc. Solo caratteri alfanumerici, trattini e apostrofi sono permessi.'
            placeholder='Es. Finca las Marias'
            maxLength={NAME_MAX_LENGTH}
          />
          <InputLabelDescription
            label='Descrivi la tua proprietà'
            value={description}
            onChange={(value) => {
              // Only allow alphanumeric, hyphen and apostrophe
              const sanitizedValue = value.replace(/[^a-zA-Z0-9\s'-]/g, '')
              setDescription(sanitizedValue)
            }}
            description='Includi una breve descrizione della tua proprietà. Solo caratteri alfanumerici, trattini e apostrofi sono permessi.'
            placeholder='Es. Bellissima vista sul mare, a 5 minuti dalla spiaggia'
            isTextArea
            maxLength={DESCRIPTION_MAX_LENGTH}
          />
        </div>

        <Button
          color='white'
          backgroundColor='var(--blue)'
          text='Continua'
          fontSize='14px'
          onClick={() => {
            handleSaveAndContinue()
          }}
        />
      </div>


    </MobilePageStart>
  )
}

export default Page