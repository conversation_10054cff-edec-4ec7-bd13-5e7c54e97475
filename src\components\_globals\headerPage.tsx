import React, { useContext } from 'react'
import styles from './globals.module.css'
import { ArrowLeft, XIcon } from 'lucide-react'


interface HeaderProps {
  title?: string
  actionLeftIcon?: () => void,
  actionRightIcon?: () => void,
}


function HeaderPage({ title, actionLeftIcon, actionRightIcon }: HeaderProps) {

  return (
    <section className={styles.header} style={{padding: '0 1.25rem'}}>
      <div className='flex flex-row items-center gap-8'>
        <ArrowLeft onClick={actionLeftIcon} width={20}/>
        <p style={{fontSize: '.95rem', fontWeight: 500}}>
          {title}
        </p>
      </div>

      <XIcon onClick={actionRightIcon} width={20}/>
    </section>

  )
}

export default HeaderPage