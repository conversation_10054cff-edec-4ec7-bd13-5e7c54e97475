'use client'
import React, { useContext, useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderLogo from '@/components/_globals/headerLogo'
import Navbar from '@/components/_globals/navbar'
import EnhancedMonthTable from '@/components/calendar/monthly/EnhancedMonthTable'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import Button from '@/components/buttons/Button'
import Modal from '@/components/_globals/modal'
import { PropertyContext } from '@/components/_context/PropertyContext'
import { getMonthDailyRate } from '@/services/api'
import { useRouter } from 'next/navigation'
import { useCalendar } from '@/components/_context/CalendarContext'
import { getMonthDateRange } from '@/utils/dateUtils'

function Page() {
  const router = useRouter()
  const { properties, isLoadingProperties, fetchProperties }: {
    properties: { id: string; name: string }[];
    isLoadingProperties: boolean;
    fetchProperties: () => Promise<void>;
  } = useContext(PropertyContext)


  const [selectedProperty, setSelectedProperty] = useState<string | any>(properties.filter((property:any) => property.is_onboarded)[0]?.id ?? null)
  const [currentDate, setCurrentDate] = useState<Date>(new Date())
  const [reservations, setReservations] = useState<any[]>([])

  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)
  const [isEdit, setIsEdit] = useState(false)

  const [isLoading, setIsLoading] = useState(false)
  const [isLoading2, setIsLoading2] = useState(false)
  const [dayRates, setDayRates] = useState<any>([])
  const [forceRefresh, setForceRefresh] = useState(0)

  const handleClickCalendarDay = (date: Date) => {
    // If we don't have a start date yet, set it
    if (!startDate) {
      setStartDate(date);
      return;
    }

    // If we have a start date but no end date
    if (startDate && !endDate) {
      // If the new date is before the start date, swap them
      if (date < startDate) {
        setEndDate(startDate);
        setStartDate(date);
      } else {
        // Otherwise, set the end date normally
        setEndDate(date);
      }
      return;
    }

    // If we already have both dates, start a new selection
    if (startDate && endDate) {
      setStartDate(date);
      setEndDate(null);
    }
  }
  // Use the calendar context for fetching data
  const { fetchBookings, fetchBlockedDates } = useCalendar();

  const handleFetchPropertyCalendar = async (currentDate: Date) => {
    const { start, end } = calculateStartAndEndofCurrentMonth(currentDate);
    setIsLoading(true);

    // Use the context methods to fetch data with caching
    const [bookings, blocked] = await Promise.all([
      fetchBookings(selectedProperty, start, end),
      fetchBlockedDates(selectedProperty, start, end)
    ]);

    // Map over the bookings array to add a 'type' property to each object
    // Filter out cancelled reservations
    const reservationsWithType = bookings
      .filter((booking: any) => booking.status !== 'cancelled')
      .map((booking: any) => ({
        ...booking,
        type: 'reservation',
      }));

    // Map over the blocked dates array to add a 'type' property to each object
    const blockedWithType = blocked.map((block: any) => ({
      ...block,
      type: 'block',
    }));

    // Combine both arrays and update reservations
    setReservations([...reservationsWithType, ...blockedWithType]);
    setIsLoading(false);
  }
  const handleFetchPropertyDailyRate = async () => {
    const call = await getMonthDailyRate(selectedProperty, currentDate.getFullYear().toString(), (currentDate.getMonth() + 1).toString())

    setDayRates(call.data)
  }



  // Use the utility function for date range calculation
  const calculateStartAndEndofCurrentMonth = (date: Date) => {
    return getMonthDateRange(date);
  }

  useEffect(() => {
    fetchProperties()
  }, [])

  useEffect(() => {
    if (startDate && endDate) {
      setIsEdit(true)

    }
  }, [startDate, endDate])

  useEffect(() => {
    if (selectedProperty) {
      handleFetchPropertyCalendar(currentDate)
      handleFetchPropertyDailyRate()
    }
  }, [selectedProperty, currentDate, forceRefresh])

  // Force refresh when component mounts
  useEffect(() => {
    setForceRefresh(prev => prev + 1)

    // Example of how to use the NavbarSafeToast
    // This is just for demonstration - you can remove this in production
    // Uncomment to test:
    // setTimeout(() => {
    //   showNavbarSafeToast({
    //     message: 'Calendario caricato con successo',
    //     type: 'success'
    //   });
    // }, 1000);
  }, [])

  return (
    <MobilePageStart
      isNavbar
    >
      <HeaderLogo />
      <div
        style={{
          width: '100%',
          padding: '0 20px',
          marginTop: '30px',
          scrollbarColor: 'transparent transparent',

        }}
      >
        {isLoadingProperties ? (
          <div className='h-[400px] w-full flex flex-col items-center justify-center'>
            <p className='text-[23px]'>Caricamento proprietà...</p>
          </div>
        ) : (
          <>
            <InputSelectLabel
              label='Selezionare Proprietà'
              placeholder='Seleziona una proprietà'
              onSelect={setSelectedProperty}
              options={
                properties
                .filter((property: any) => property.is_onboarded)
                .map((property: any) => ({
                  value: property.id,
                  label: property.name
                })
                )}
              value={properties.find((property: any) => property.id === selectedProperty)?.name ?? ''}
            />
            <br />

            {
              selectedProperty ?
              <EnhancedMonthTable
                onCurrentMonth={(value) => {
                  setDayRates([])
                  setCurrentDate(value)
                }}
                onSelectDay={handleClickCalendarDay}
                selectedStartDate={startDate}
                selectedEndDate={endDate}
                res={reservations}
                rates={dayRates}
                property={selectedProperty}
              />
              :
              <div className='h-[400px] w-full flex flex-col items-center justify-center'>
                  <p className='text-[23px]'>Selezionare una proprietà</p>
                  <p className='text-[13px]'>Nessuna proprietà e disponibile, attiva o in lista</p>
              </div>
            }
            <br />
            {
              startDate &&
              endDate &&

              <Button
                text='Edit'
                onClick={() => setIsEdit(true)}
                color='white'
                backgroundColor='var(--blue)'
              />
            }
            {
              isEdit &&
              <Modal
                isOpen={isEdit}
                onClose={() => setIsEdit(false)}
              >
                <div className='flex flex-col gap-2 w-full'>
                  <div className='mb-6'>
                    <p style={{ fontSize: '13px' }}>Entrata - {startDate && startDate.toLocaleDateString('it-IT', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</p>

                    <p style={{ fontSize: '13px' }}>Uscita - {endDate && endDate.toLocaleDateString('it-IT', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}</p>
                  </div>
                  <Button
                    text='Blocca Date'
                    onClick={() => {
                      const formatDate = (date: any) => {
                        if (!date) return '';
                        const d = new Date(date);

                        // Extract date components with `toLocaleString()`
                        const formattedDate = d.toLocaleDateString('en-GB', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                        });

                        return formattedDate.split('/').reverse().join('-'); // Convert "DD/MM/YYYY" to "YYYY-MM-DD"
                      };

                      router.push(`/dashboard/calendar/block?start_date=${startDate && formatDate(startDate)}&end_date=${endDate && formatDate(endDate)}&property=${selectedProperty}`)
                    }}
                    color='white'
                    backgroundColor='var(--blue)'
                  />
                  <Button
                    text='Riservazione Manuale'
                    onClick={() => {
                      const formatDate = (date: any) => {
                        if (!date) return '';
                        const d = new Date(date);

                        // Extract date components with `toLocaleString()`
                        const formattedDate = d.toLocaleDateString('en-GB', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                        });

                        return formattedDate.split('/').reverse().join('-'); // Convert "DD/MM/YYYY" to "YYYY-MM-DD"
                      };

                      router.push(`/dashboard/calendar/manual-reservation?start_date=${startDate && formatDate(startDate)}&end_date=${endDate && formatDate(endDate)}&property=${selectedProperty}`)
                    }}
                    color='white'
                    backgroundColor='var(--blue)'
                  />
                  <Button
                    text='Crea Stagione'
                    onClick={() => {
                      const formatDate = (date: any) => {
                        if (!date) return '';
                        const d = new Date(date);

                        // Extract date components with `toLocaleString()`
                        const formattedDate = d.toLocaleDateString('en-GB', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                        });

                        return formattedDate.split('/').reverse().join('-'); // Convert "DD/MM/YYYY" to "YYYY-MM-DD"
                      };

                      router.push(`/dashboard/calendar/pricing?start_date=${startDate && formatDate(startDate)}&end_date=${endDate && formatDate(endDate)}&property=${selectedProperty}`)
                      //router.push(`/dashboard/property/${selectedProperty}/tarifs`)

                    }}
                    color='white'
                    backgroundColor='var(--blue)'
                  />

                  <br />
                  <Button
                    text='Chiudi'
                    onClick={() => {
                      setIsEdit(false)
                      setStartDate(null)
                      setEndDate(null)
                    }}
                    color='white'
                    backgroundColor='tomato'
                  />
                </div>
              </Modal>
            }
          </>
        )}
      </div>

      <Navbar />
    </MobilePageStart>
  )
}

export default Page