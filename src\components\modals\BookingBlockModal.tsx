import React, { useState } from 'react';
import Modal from '@/components/_globals/modal';
import Title from '@/components/titles/Title';
import Button from '@/components/buttons/Button';
import { CalendarX, AlertTriangle, Check, X, Calendar, Lock } from 'lucide-react';
import toast from 'react-hot-toast';
import { useCalendar } from '@/components/_context/CalendarContext';

interface BookingBlockModalProps {
  isOpen: boolean;
  onClose: () => void;
  blockData: {
    id: number;
    start_date: string;
    end_date: string;
    is_active?: boolean;
    reason?: string;
  };
  onDeactivate: () => void;
}

const BookingBlockModal = ({ isOpen, onClose, blockData, onDeactivate }: BookingBlockModalProps) => {
  const [isDeactivating, setIsDeactivating] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const { deactivateBlock } = useCalendar();

  const handleDeactivateBlock = async () => {
    try {
      setIsDeactivating(true);
      const success = await deactivateBlock(blockData.id.toString());

      if (success) {
        onDeactivate();
        onClose();
      } else {
        throw new Error('Deactivation failed');
      }
    } catch (error) {
      console.error('Error deactivating booking block:', error);
      setIsDeactivating(false);
    }
  };

  // Format dates for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('it-IT', {
      weekday: 'short',
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate the number of days in the block
  const getDaysCount = () => {
    const startDate = new Date(blockData.start_date);
    const endDate = new Date(blockData.end_date);
    const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
    return diffDays;
  };

  if (showConfirmation) {
    return (
      <Modal isOpen={isOpen} onClose={onClose}>
        <div className="w-full flex flex-col items-center">
          <div className="w-16 h-16 rounded-full bg-amber-100 flex items-center justify-center mb-4">
            <AlertTriangle size={32} className="text-amber-500" />
          </div>

          <Title
            title="Conferma Disattivazione"
            subtitle="Sei sicuro di voler disattivare questo blocco di prenotazione?"
          />

          <div className="w-full p-6 my-4 bg-white rounded-lg border border-gray-200 shadow-sm">
            <p className="text-gray-700 mb-2">
              Questo renderà disponibili per la prenotazione le seguenti date:
            </p>
            <p className="font-medium text-gray-800">
              {formatDate(blockData.start_date)} - {formatDate(blockData.end_date)}
            </p>
            <p className="text-sm text-gray-500 mt-2">
              ({getDaysCount()} giorni)
            </p>
          </div>

          <div className="flex w-full gap-4 px-6">
            <div style={{ width: '50%' }}>
              <Button
                text="Annulla"
                color="black"
                backgroundColor="#f3f4f6"
                onClick={() => setShowConfirmation(false)}
              />
            </div>
            <div style={{ width: '50%' }}>
              <Button
                text={isDeactivating ? "Disattivazione in corso..." : "Conferma Disattivazione"}
                color="white"
                backgroundColor="#ef4444"
                onClick={handleDeactivateBlock}
                disabled={isDeactivating}
              />
            </div>
          </div>
        </div>
      </Modal>
    );
  }

  // Main modal content
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="w-full flex flex-col items-center">
        <div className="w-16 h-16 rounded-full bg-amber-100 flex items-center justify-center mb-4">
          <Lock size={32} className="text-amber-500" />
        </div>

        <Title
          title="Attenzione"
          subtitle="Un blocco prenotazione è attivo in questo giorno"
        />

        <div className="w-full p-6 my-4 bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* Status indicator */}
          <div className="flex items-center mb-4 pb-3 border-b border-gray-100">
            <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
              {blockData.is_active !== false ? (
                <Check size={20} className="text-amber-500" />
              ) : (
                <X size={20} className="text-gray-500" />
              )}
            </div>
            <div>
              <p className="text-sm text-gray-500">Stato</p>
              <p className="font-medium text-gray-800">
                {blockData.is_active !== false ? 'Attivo' : 'Inattivo'}
              </p>
            </div>
          </div>

          {/* Date range */}
          <div className="flex items-center mb-4 pb-3 border-b border-gray-100">
            <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
              <Calendar size={20} className="text-amber-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Periodo Bloccato</p>
              <p className="font-medium text-gray-800">
                {formatDate(blockData.start_date)} - {formatDate(blockData.end_date)}
              </p>
              <p className="text-sm text-gray-500 mt-1">
                ({getDaysCount()} giorni)
              </p>
            </div>
          </div>

          {/* Reason if available */}
          {blockData.reason && (
            <div className="flex items-center mb-4 pb-3 border-b border-gray-100">
              <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                <AlertTriangle size={20} className="text-amber-500" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Motivo</p>
                <p className="font-medium text-gray-800">{blockData.reason}</p>
              </div>
            </div>
          )}

          {/* Availability message */}
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
              <CalendarX size={20} className="text-amber-500" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Disponibilità</p>
              <p className="font-medium text-gray-800">
                {blockData.is_active !== false
                  ? "Queste date non sono attualmente disponibili per la prenotazione"
                  : "Questo blocco è inattivo e non influisce sulla disponibilità"}
              </p>
            </div>
          </div>
        </div>

        <div className="flex w-full gap-4 px-6">
          <div style={{ width: '50%' }}>
            <Button
              text="Chiudi"
              color="black"
              backgroundColor="#f3f4f6"
              onClick={onClose}
            />
          </div>
          <div style={{ width: '50%' }}>
            <Button
              text="Disattiva Blocco"
              color="white"
              backgroundColor="#ef4444"
              onClick={() => setShowConfirmation(true)}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default BookingBlockModal;
