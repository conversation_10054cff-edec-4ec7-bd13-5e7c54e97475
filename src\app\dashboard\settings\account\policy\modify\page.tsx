'use client'
import React, { useEffect, useState } from 'react'
import MobilePage from '@/components/_globals/mobilePage'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import MultiSelect from '@/components/inputs/multiSelect'
import Button from '@/components/buttons/Button'
import InputCounter from '@/components/inputs/inputCounter'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import Link from 'next/link'
import ButtonSection from '@/components/buttons/ButtonSection'


function Page() {
  const router = useRouter()

  const [selectedProperty, setSelectedProperty] = useState(['1'])
  const [selectedPolicy, setSelectedPolicy] = useState(['1'])



  return (
    <MobilePageStart>
      <HeaderPage title='Account Policy'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard/settings`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-4'>

        <MultiSelect
          isMultiSelect={false}
          title='Seleziona propietà per la modifica'
          value={selectedProperty}
          onChange={(selected) => {
            setSelectedProperty(selected)
          }}
          options={[
            {
              id: '1',
              title: 'Casa 1',
              description: 'Via Roma 1, 00100, Roma'
            },
            {
              id: '2',
              title: 'Casa 2',
              description: 'Via Roma 2, 00100, Roma'
            },
            {
              id: '3',
              title: 'Casa 3',
              description: 'Via Roma 3, 00100, Roma'
            },
          ]}
        />

        <MultiSelect
          isMultiSelect={false}
          title='Selezionare la nuova policy'
          value={selectedPolicy}
          onChange={(selected) => {
            setSelectedPolicy(selected)
          }}
          options={[
            {
              id: '1',
              title: 'Non rimborsabile',
              description: 'Il cliente non riceverà il rimborso in caso di cancellazione'
            },
            {
              id: '2',
              title: 'Parzialmente rimborsabile',
              description: 'Il cliente riceverà un rimborso parziale in caso di cancellazione'
            },
            {
              id: '3',
              title: 'Totalmente rimborsabile',
              description: 'Il cliente riceverà un rimborso totale in caso di cancellazione'
            },
          ]}
        />

        <Button
          text='Aggiorna Policy'
          onClick={() => {}}
          color='white'
          backgroundColor='var(--green)'
        />


      </div>


    </MobilePageStart>
  )
}

export default Page