'use client'
import React, { useContext, useEffect, useState, useRef } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import { useRouter } from 'next/navigation'
import HeaderPage from '@/components/_globals/headerPage'
import { getToken } from '@/services/api';
import { NotificationsContext } from '@/components/_context/NotificationsContext'
import NotificationsWrapper from '@/components/_context/NotificationsWrapper'
import toast from 'react-hot-toast'
import { CheckCircleIcon, ArrowPathIcon } from '@heroicons/react/16/solid'

function Page() {
  return (
    <NotificationsWrapper>
      <NotificationsContent />
    </NotificationsWrapper>
  )
}

function NotificationsContent() {
  const router = useRouter()
  const { notifications, setNotifications, isLoading, markNotificationAsRead, refreshNotifications } = useContext(NotificationsContext)
  const [connectionStatus, setConnectionStatus] = useState<string>('Disconnesso')
  const [isConnected, setIsConnected] = useState<boolean>(false)
  const socketRef = useRef<WebSocket | null>(null)
  const [error, setError] = useState<string | null>(null)

  // Function to connect to WebSocket
  const connectWebSocket = () => {
    const token = getToken()
    if (!token) {
      setError('Autenticazione richiesta')
      return
    }

    // Determine WebSocket protocol based on current protocol
    const wsScheme = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
    const wsUrl = `${wsScheme}//${process.env.NEXT_PUBLIC_DOMAIN}/ws/notifications/`

    // Append token as query parameter
    const wsUrlWithToken = `${wsUrl}?token=${encodeURIComponent(token)}`
    setConnectionStatus('Connessione in corso...')

    try {
      // Close existing connection if any
      if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
        socketRef.current.close()
      }

      // Create new WebSocket connection
      socketRef.current = new WebSocket(wsUrlWithToken)

      socketRef.current.onopen = () => {
        setIsConnected(true)
        setError(null)
      }

      socketRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)


          if (message.type === 'notification' && message.data) {
            // Add new notification to the beginning of the array
            setNotifications(prev => [message.data, ...prev])
            toast.success('Nuova notifica ricevuta')
          } else if (message.type === 'notification_update') {
            // Update notification status
            updateNotificationStatus(message.data)
          }
        } catch (error) {
          console.error('Error processing message:', error)
          setError('Errore durante l\'elaborazione della notifica')
        }
      }

      socketRef.current.onclose = (event) => {
        setIsConnected(false)
      }

      socketRef.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        setError('Si è verificato un errore di connessione')
        setConnectionStatus('Errore')
        setIsConnected(false)
      }
    } catch (error) {
      console.error('Error creating WebSocket:', error)
      setError('Impossibile stabilire la connessione WebSocket')
      setConnectionStatus('Errore')
      setIsConnected(false)
    }
  }

  // Connect to WebSocket on component mount
  useEffect(() => {
    connectWebSocket()

    // Set up a ping interval to keep the connection alive
    const pingInterval = setInterval(() => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        // Send a ping message to keep the connection alive
        socketRef.current.send(JSON.stringify({ action: 'ping' }))
      }
    }, 30000) // Send ping every 30 seconds

    // Cleanup function
    return () => {
      clearInterval(pingInterval)
      if (socketRef.current) {
        socketRef.current.close()
      }
    }
  }, [])

  // Function to update notification status
  const updateNotificationStatus = (data: any) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === data.id
          ? { ...notification, is_read: data.is_read, read_at: new Date().toISOString() }
          : notification
      )
    )
  }

  // Function to mark notification as read
  const markAsRead = (notificationId: string) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      socketRef.current.send(JSON.stringify({
        action: 'mark_as_read',
        notification_ids: [notificationId]
      }))

      // Update local state immediately for better UX
      markNotificationAsRead(notificationId)
    } else {
      toast.error('Connessione persa. Riconnettere.')
    }
  }

  function timeAgoInItalian(timestamp: string): string {
    const now = new Date();
    const eventDate = new Date(timestamp);

    const timeDifference = now.getTime() - eventDate.getTime();
    const seconds = timeDifference / 1000;
    const minutes = seconds / 60;
    const hours = minutes / 60;
    const days = hours / 24;
    const weeks = days / 7;
    const months = days / 30;

    // If it's less than 24 hours, show the time
    if (hours < 24) {
      const hoursFormatted = eventDate.getHours().toString().padStart(2, '0');
      const minutesFormatted = eventDate.getMinutes().toString().padStart(2, '0');
      return `${hoursFormatted}:${minutesFormatted}`;
    }
    // Otherwise, show the relative time
    else if (days < 7) {
      return `${Math.floor(days)} giorno${Math.floor(days) > 1 ? 'i' : ''} fa`;
    } else if (weeks < 4) {
      return `${Math.floor(weeks)} settimana${Math.floor(weeks) > 1 ? 'e' : ''} fa`;
    } else if (months < 12) {
      return `${Math.floor(months)} mese${Math.floor(months) > 1 ? 'i' : ''} fa`;
    } else {
      const years = Math.floor(months / 12);
      return `${years} anno${years > 1 ? 'i' : ''} fa`;
    }
  }

  return (
    <MobilePageStart
      noPadding
    >
      <HeaderPage
        actionLeftIcon={() => {
          router.back()
        }}
        title='Notifiche'
        actionRightIcon={() => {
          router.push('/dashboard')
        }}
      />

      <div
        style={{
          width: '100%',
          padding: '0 20px',
          marginTop: '15px',
          scrollbarColor: 'transparent transparent',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >


        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
            marginBottom: '20px',
          }}
        >

          <button
            onClick={() => {
              refreshNotifications();
              toast.success('Aggiornamento notifiche...');
            }}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '8px',
              borderRadius: '50%',
              backgroundColor: 'white',
              border: 'none',
              boxShadow: '0px 0px 10px 0px #00000010',
              cursor: 'pointer',
            }}
            disabled={isLoading}
          >
            <ArrowPathIcon
              width={16}
              color='var(--accent)'
              className={isLoading ? 'animate-spin' : ''}
            />
          </button>
        </div>

        {/* Notifications list */}
        <div style={{ width: '100%', maxHeight: '75vh', overflowY: 'auto', paddingRight: '5px' }}>
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 3 }).map((_, index) => (
              <div
                key={`skeleton-${index}`}
                style={{
                  padding: '15px',
                  borderRadius: '15px',
                  backgroundColor: 'white',
                  boxShadow: '0px 0px 10px 0px #00000010',
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '10px',
                  marginBottom: '15px',
                  position: 'relative',
                  borderLeft: '3px solid #E0E0E0',
                  opacity: 0.7,
                }}
              >
                {/* Title skeleton */}
                <div style={{
                  height: '14px',
                  width: '60%',
                  backgroundColor: '#E0E0E0',
                  borderRadius: '4px',
                }} />

                {/* Message skeleton */}
                <div style={{
                  height: '12px',
                  width: '90%',
                  backgroundColor: '#E0E0E0',
                  borderRadius: '4px',
                }} />
                <div style={{
                  height: '12px',
                  width: '80%',
                  backgroundColor: '#E0E0E0',
                  borderRadius: '4px',
                }} />

                {/* Footer skeleton */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: '5px'
                }}>
                  <div style={{
                    height: '10px',
                    width: '30%',
                    backgroundColor: '#E0E0E0',
                    borderRadius: '4px',
                  }} />

                  <div style={{
                    height: '10px',
                    width: '20%',
                    backgroundColor: '#E0E0E0',
                    borderRadius: '4px',
                  }} />
                </div>
              </div>
            ))
          ) : notifications.length > 0 ? (
            notifications.map((notification: any) => (
              <div
                key={notification.id || notification.created_at}
                style={{
                  padding: '15px',
                  borderRadius: '15px',
                  backgroundColor: notification.is_read ? 'white' : '#F0F8FF',
                  boxShadow: '0px 0px 10px 0px #00000010',
                  width: '100%',
                  fontSize: '12px',
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '10px',
                  marginBottom: '15px',
                  position: 'relative',
                  borderLeft: notification.is_read ? '3px solid #E0E0E0' : '3px solid var(--accent)',
                }}
              >
                {/* Title if available */}
                {notification.title && (
                  <h3 style={{
                    fontSize: '14px',
                    fontWeight: 'bold',
                    margin: 0,
                    color: '#000000DE'
                  }}>
                    {notification.title}
                  </h3>
                )}

                {/* Message content */}
                <p style={{
                  color: notification.is_read ? '#00000070' : '#000000DE',
                  margin: 0,
                  lineHeight: '1.4'
                }}>
                  {notification.message}
                </p>

                {/* Footer with timestamp and read status */}
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginTop: '5px'
                }}>
                  <p style={{
                    color: 'var(--accent)',
                    fontSize: '11px',
                    margin: 0
                  }}>
                    {timeAgoInItalian(notification.created_at)}
                  </p>

                  {notification.is_read ? (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '3px',
                      color: '#4CAF50',
                      fontSize: '11px'
                    }}>
                      <CheckCircleIcon width={12} />
                      <span>Letto</span>
                    </div>
                  ) : (
                    <button
                      onClick={() => markAsRead(notification.id)}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: 'var(--accent)',
                        fontSize: '11px',
                        cursor: 'pointer',
                        padding: '3px 8px',
                        borderRadius: '10px',
                        backgroundColor: '#F5F5F5',
                      }}
                    >
                      Segna come letto
                    </button>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div
              style={{
                width: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '10px',
                padding: '30px 0',
                color: '#00000070',
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 10.5V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h16a2 2 0 0 0 2-2v-7.5" />
                <path d="M22 6 12 13 2 6" />
              </svg>
              <p>Non ci sono notifiche</p>
            </div>
          )}
        </div>
      </div>
    </MobilePageStart>
  )
}

export default Page