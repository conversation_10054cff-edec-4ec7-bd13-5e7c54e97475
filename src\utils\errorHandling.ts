import toast from 'react-hot-toast';

// WebSocket error types
export enum WebSocketErrorType {
  CONNECTION = 'connection',
  AUTHENTICATION = 'authentication',
  MESSAGE = 'message',
  SERVER = 'server',
  NETWORK = 'network',
  UNKNOWN = 'unknown'
}

// WebSocket error codes and their meanings
export const WebSocketCloseCode = {
  // Standard close codes
  NORMAL_CLOSURE: 1000,
  GOING_AWAY: 1001,
  PROTOCOL_ERROR: 1002,
  UNSUPPORTED_DATA: 1003,
  NO_STATUS: 1005,
  ABNORMAL_CLOSURE: 1006,
  INVALID_FRAME_PAYLOAD_DATA: 1007,
  POLICY_VIOLATION: 1008,
  MESSAGE_TOO_BIG: 1009,
  MISSING_EXTENSION: 1010,
  INTERNAL_ERROR: 1011,
  SERVICE_RESTART: 1012,
  TRY_AGAIN_LATER: 1013,
  
  // Custom error codes for chat service
  AUTH_MISSING: 4001,
  AUTH_INVALID: 4002,
  AUTH_ERROR: 4003,
  CHAT_ID_MISSING: 4004,
  CHAT_NOT_FOUND: 4005,
  ACCESS_DENIED: 4006,
  CHAT_DETAILS_ERROR: 4007
};

interface ErrorDetails {
  type: WebSocketErrorType;
  message: string;
  recoverable: boolean;
  userMessage: string;
}

// Map error codes to friendly error messages
export function getErrorDetails(code: number): ErrorDetails {
  const errors: Record<number, ErrorDetails> = {
    [WebSocketCloseCode.NORMAL_CLOSURE]: {
      type: WebSocketErrorType.CONNECTION,
      message: 'Connection closed normally',
      recoverable: true,
      userMessage: 'La connessione è stata chiusa. Riconnessione in corso...'
    },
    [WebSocketCloseCode.GOING_AWAY]: {
      type: WebSocketErrorType.CONNECTION,
      message: 'Server going away',
      recoverable: true,
      userMessage: 'La connessione è stata interrotta. Riconnessione in corso...'
    },
    [WebSocketCloseCode.ABNORMAL_CLOSURE]: {
      type: WebSocketErrorType.NETWORK,
      message: 'Connection lost unexpectedly',
      recoverable: true,
      userMessage: 'Connessione persa. Tentativo di riconnessione in corso...'
    },
    [WebSocketCloseCode.AUTH_MISSING]: {
      type: WebSocketErrorType.AUTHENTICATION,
      message: 'Authentication token missing',
      recoverable: false,
      userMessage: 'Errore di autenticazione. Effettua nuovamente il login.'
    },
    [WebSocketCloseCode.AUTH_INVALID]: {
      type: WebSocketErrorType.AUTHENTICATION,
      message: 'Invalid authentication token',
      recoverable: false,
      userMessage: 'Il token di autenticazione non è valido. Effettua nuovamente il login.'
    },
    [WebSocketCloseCode.AUTH_ERROR]: {
      type: WebSocketErrorType.AUTHENTICATION,
      message: 'Authentication error',
      recoverable: false,
      userMessage: 'Errore durante l\'autenticazione. Riprova più tardi.'
    },
    [WebSocketCloseCode.CHAT_ID_MISSING]: {
      type: WebSocketErrorType.SERVER,
      message: 'Chat ID is missing',
      recoverable: false,
      userMessage: 'ID della chat mancante. Ricarica la pagina.'
    },
    [WebSocketCloseCode.CHAT_NOT_FOUND]: {
      type: WebSocketErrorType.SERVER,
      message: 'Chat not found',
      recoverable: false,
      userMessage: 'La chat richiesta non esiste.'
    },
    [WebSocketCloseCode.ACCESS_DENIED]: {
      type: WebSocketErrorType.AUTHENTICATION,
      message: 'Permission denied',
      recoverable: false,
      userMessage: 'Non hai i permessi per accedere a questa chat.'
    },
    [WebSocketCloseCode.CHAT_DETAILS_ERROR]: {
      type: WebSocketErrorType.SERVER,
      message: 'Error getting chat details',
      recoverable: true,
      userMessage: 'Errore nel caricamento della chat. Riprova più tardi.'
    }
  };
  
  return errors[code] || {
    type: WebSocketErrorType.UNKNOWN,
    message: `Unknown close code: ${code}`,
    recoverable: true,
    userMessage: 'Si è verificato un errore di connessione. Riconnessione in corso...'
  };
}

// Display appropriate error message to user based on error type
export function handleWebSocketError(error: string | number): void {
  let errorDetails: ErrorDetails;
  
  if (typeof error === 'number') {
    errorDetails = getErrorDetails(error);
  } else {
    // Parse error message to determine type
    if (error.includes('token') || error.includes('auth')) {
      errorDetails = {
        type: WebSocketErrorType.AUTHENTICATION,
        message: error,
        recoverable: false,
        userMessage: 'Errore di autenticazione. Effettua nuovamente il login.'
      };
    } else if (error.includes('connect')) {
      errorDetails = {
        type: WebSocketErrorType.CONNECTION,
        message: error,
        recoverable: true,
        userMessage: 'Impossibile connettersi alla chat. Riprova più tardi.'
      };
    } else {
      errorDetails = {
        type: WebSocketErrorType.UNKNOWN,
        message: error,
        recoverable: true,
        userMessage: 'Si è verificato un errore. Riprova più tardi.'
      };
    }
  }
  
  // Log detailed error for developers
  console.error(`WebSocket Error (${errorDetails.type}): ${errorDetails.message}`);
  
  // Show user-friendly message
  if (!errorDetails.recoverable) {
    toast.error(errorDetails.userMessage);
  } else {
    toast(errorDetails.userMessage, {
      icon: '🔄'
    });
  }
  
  // For authentication errors, could redirect to login
  if (errorDetails.type === WebSocketErrorType.AUTHENTICATION) {
    // Could redirect to login or trigger token refresh
    // window.location.href = '/login';
  }
}
