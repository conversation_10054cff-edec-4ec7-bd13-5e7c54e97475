import React from 'react'
import { <PERSON>R<PERSON>, CheckI<PERSON>, <PERSON>, XIcon } from "lucide-react"
import { useRouter } from 'next/navigation'



interface ButtonSectionProps {
    ticker?: boolean,
    completed: boolean,
    title: string,
    desciption?: string | null,
    href?: string
    onClick?: () => void

    noIcon?: boolean
    disabled?: boolean
    backgroundColor?: string
  }
  
  const ButtonSectionFunction = ({
    ticker,
    completed,
    title,
    desciption,
    href,
    onClick,
    noIcon,
    backgroundColor,
    disabled
  }: ButtonSectionProps) => {
    const router = useRouter()
  
    return (
      <div
        onClick={() => {
          if (disabled){
            return
          }

          href && router.push(href)
          onClick && onClick()

        }}
        className='flex flex-row bg-[white] p-3 rounded-[15px] items-center justify-between w-full'
        style={{
          opacity: disabled ? 0.5 : 1,
          boxShadow: '0 0 5px 15pxrgba(0, 0, 0, 0.19)',
          backgroundColor: `${backgroundColor ? backgroundColor : 'white'}`
        }}
      >
  
        <div className='flex flex-row items-center gap-3'>
          {/* Ticker green/red */}
          {
            ticker &&
            <div
              style={{
                minHeight: '22px',
                minWidth: '22px',
                borderRadius: '50%',
                background: `${completed ? 'yellowgreen' : 'tomato'}`
              }}
              className='flex flex-row items-center justify-center'
            >
              {
                completed
                  ?
                  <CheckIcon width={17} color='white' />
                  :
                  <XIcon width={17} color='white' />
              }
    
            </div>
          }
          <div>
            <p className='text-[14px]'>{title}</p>
            {
              desciption &&
              <p className='text-[12px] text-[#00000090] mt-1'>{desciption}</p>
            }

          </div>
        </div>
  
            {
              !noIcon &&
              <ArrowRight width={20} color='black' />
            }
      </div>
    )
  
  }

  export default ButtonSectionFunction