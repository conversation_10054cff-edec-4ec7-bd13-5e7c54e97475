.container {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
}

.card {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  width: 100%;
  max-width: 480px;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  margin-top: -60px; /* Offset for HeaderLogo height */
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.infoSection {
  margin-bottom: 2rem;
}

.label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.value {
  font-size: 1rem;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}


.acceptButton {
  width: 100%;
  margin-top: 1rem;
}

.errorContainer {
  text-align: center;
  padding: 2rem;
}

.errorTitle {
  font-size: 1.5rem;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.errorText {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}
