import { SupportMessage, WebSocketMessage, ConnectionStatus, TypingStatus, SupportAttachment } from '@/types/support';

interface PendingWebSocketMessage {
  id: string;
  message: WebSocketMessage;
  timestamp: number;
  attempts: number;
}

export class SupportChatWebSocket {
  private ws: WebSocket | null = null;
  private chatId: string;
  private token: string;
  private wsUrl: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnected = false;
  private messageQueue: WebSocketMessage[] = [];
  private pendingMessages: Map<string, PendingWebSocketMessage> = new Map();
  private messageRetryTimeout: NodeJS.Timeout | null = null;
  private pingInterval: NodeJS.Timeout | null = null;
  private pingTimeoutRef: NodeJS.Timeout | null = null;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private typingTimeout: NodeJS.Timeout | null = null;
  private isCurrentlyTyping = false;  // Event callbacks
  public onConnectionStatusChange?: (status: ConnectionStatus) => void;
  public onMessageReceived?: (message: SupportMessage) => void;
  public onError?: (error: string) => void;
  public onTypingStatusChange?: (status: TypingStatus) => void;
  public onMessageConfirmed?: (clientMessageId: string, serverMessageId: string) => void;
  public onFileUploaded?: (messageId: string, attachments: SupportAttachment[]) => void;
  public onUploadProgress?: (messageId: string, progress: number) => void;
  public onMessageReadStatusUpdate?: (messageId: string, isRead: boolean) => void;
  public onMessagesReadStatusUpdate?: (messageIds: string[], isRead: boolean) => void;
  
  constructor(chatId: string, token: string, baseUrl?: string) {
    this.chatId = chatId;
    this.token = token;
    
    // Determine WebSocket protocol based on current protocol (browser-compatible)
    const wsProtocol = typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    
    // Use the proper URL format from the documentation
    // If baseUrl is provided, use it, otherwise construct from window.location
    if (baseUrl) {
      this.wsUrl = `${baseUrl}/ws/support/${chatId}/?token=${encodeURIComponent(token)}`;
    } else if (typeof window !== 'undefined') {
      const wsBaseUrl = `${wsProtocol}//${window.location.host}`;
      this.wsUrl = `${wsBaseUrl}/ws/support/${chatId}/?token=${encodeURIComponent(token)}`;
    } else {
      // Fallback for non-browser environments (SSR)
      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
      this.wsUrl = `${wsBaseUrl}/ws/support/${chatId}/?token=${encodeURIComponent(token)}`;
    }
    
    console.log('WebSocket URL:', this.wsUrl); // Debug log
  }
  public connect(): void {
    try {
      if (this.ws?.readyState === WebSocket.OPEN) {
        console.warn('WebSocket is already connected');
        return;
      }

      // Verify token is available
      if (!this.token) {
        this.handleConnectionError('Authentication token missing');
        return;
      }

      console.log('Connecting to WebSocket:', this.wsUrl);
      this.ws = new WebSocket(this.wsUrl);
      this.setupEventListeners();
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.handleConnectionError('Failed to create WebSocket connection');
    }
  }

  private setupEventListeners(): void {
    if (!this.ws) return;

    this.ws.onopen = this.handleOpen.bind(this);
    this.ws.onmessage = this.handleMessage.bind(this);
    this.ws.onclose = this.handleClose.bind(this);
    this.ws.onerror = this.handleError.bind(this);
  }

  private handleOpen(): void {
    console.log('Support chat WebSocket connected');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    this.reconnectDelay = 1000;

    this.startPingInterval();
    this.flushMessageQueue();
    
    this.onConnectionStatusChange?.({
      isConnected: true,
      reconnectAttempts: 0
    });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data: WebSocketMessage = JSON.parse(event.data);
        switch (data.type) {        case 'chat.message':
          if (data.message && data.sender && data.timestamp && data.id && data.chat_id) {
            const message: SupportMessage = {
              id: data.id,
              chat_id: data.chat_id,
              message: data.message,
              sender: data.sender,
              created_at: data.timestamp,
              user_name: data.user_name || (data.sender === 'support' ? 'Assistenza Affitti' : ''),
              attachments: data.attachments || [],
              is_read: data.is_read || false
            };

            console.log('Received message from server:', message);

            // Always emit the received message so it appears in the UI
            this.onMessageReceived?.(message);

            // Handle message confirmation for user messages
            if (data.sender === 'user' && data.client_message_id) {
              // Check if we have this message in our pending list
              if (this.pendingMessages.has(data.client_message_id)) {
                // Wait briefly before removing to avoid UI flicker
                setTimeout(() => {
                  if (data.client_message_id) {
                    this.pendingMessages.delete(data.client_message_id);
                    console.log(`✅ Message ${data.client_message_id} confirmed by server with ID: ${data.id || 'unknown'}`);
                  }
                }, 500);

                // Emit event so UI can update with confirmation
                if (data.id) {
                  this.onMessageConfirmed?.(data.client_message_id, data.id);
                }
              } else {
                console.log(`Received confirmation for unknown message: ${data.client_message_id}`);
              }
            }

            // For support messages, they are new messages that should create chat bubbles
            if (data.sender === 'support') {
              console.log('New support message received, creating chat bubble');
            }
          }
          break;
        case 'pong':
          console.debug('Received pong from server');
          // Clear ping timeout since we received a response
          if (this.pingTimeoutRef) {
            clearTimeout(this.pingTimeoutRef);
            this.pingTimeoutRef = null;
          }
          break;
        case 'error':
          console.error('Error from server:', data.message);
          this.onError?.(data.message || 'Unknown error occurred');
          break;
        case 'typing.start':
        case 'typing.stop':
          this.handleTypingStatus(data);
          break;        case 'file.uploaded':
          // Handle file uploaded notification
          if (data.message_id && data.attachments) {
            console.log('File uploaded:', data);
            // Emit file uploaded event
            this.onFileUploaded?.(data.message_id, data.attachments);
          }
          break;
        case 'upload.progress':
        case 'file.upload.progress':
          // Handle upload progress updates
          if (data.message_id && typeof data.progress === 'number') {
            console.log(`Upload progress for ${data.message_id}: ${data.progress}%`);
            // Emit progress update event
            this.onUploadProgress?.(data.message_id, data.progress);
          }
          break;
        case 'message.read.update':
          // Handle single message read status update
          if (data.message_id && typeof data.is_read === 'boolean') {
            console.log(`Message read status update: ${data.message_id} -> ${data.is_read}`);
            this.onMessageReadStatusUpdate?.(data.message_id, data.is_read);
          }
          break;
        case 'messages.read.update':
          // Handle multiple messages read status update
          if (data.message_ids && Array.isArray(data.message_ids) && typeof data.is_read === 'boolean') {
            console.log(`Multiple messages read status update: ${data.message_ids.length} messages -> ${data.is_read}`);
            this.onMessagesReadStatusUpdate?.(data.message_ids, data.is_read);
          }
          break;
        default:
          console.warn('Unknown message type:', data.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }
  private handleTypingStatus(data: any): void {
    // Only handle typing indicators from support staff, not from users
    if (data.user_id && data.sender && data.sender === 'support') {
      const isTyping = data.type === 'typing.start';
      console.log(`Support typing status: ${isTyping ? 'started' : 'stopped'}`);

      this.onTypingStatusChange?.({
        isTyping,
        userIds: [data.user_id]
      });

      // Auto-clear typing indicator after 10 seconds if no stop signal received
      if (isTyping) {
        setTimeout(() => {
          this.onTypingStatusChange?.({
            isTyping: false,
            userIds: []
          });
        }, 10000);
      }
    }
  }
  private handleClose(event: CloseEvent): void {
    console.log('Support chat WebSocket closed:', event.code, event.reason);
    this.isConnected = false;
    this.stopPingInterval();

    this.onConnectionStatusChange?.({
      isConnected: false,
      error: this.getCloseCodeMessage(event.code),
      reconnectAttempts: this.reconnectAttempts
    });

    // Handle specific close codes as per documentation
    switch (event.code) {
      case 1000: // Normal closure
        console.log('WebSocket closed normally');
        break;
      case 4001:
        this.onError?.('Authentication token missing');
        break;
      case 4002:
        this.onError?.('Invalid authentication token');
        // Could trigger token refresh here
        break;
      case 4004:
        this.onError?.('Chat session not found');
        break;
      case 4005:
        this.onError?.('Chat not found');
        break;
      case 4006:
        this.onError?.('Permission denied');
        break;
      default:
        // Attempt to reconnect for unexpected closures
        if (this.shouldReconnect(event.code)) {
          this.scheduleReconnection();
        } else {
          this.onError?.(this.getCloseCodeMessage(event.code));
        }
    }
  }

  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.handleConnectionError('WebSocket connection error');
  }

  private shouldReconnect(closeCode: number): boolean {
    // Don't reconnect for authentication or permission errors
    const nonRecoverableErrors = [4001, 4002, 4003, 4004, 4005, 4006, 4007];
    return !nonRecoverableErrors.includes(closeCode) && this.reconnectAttempts < this.maxReconnectAttempts;
  }

  private getCloseCodeMessage(code: number): string {
    const codeMessages: Record<number, string> = {
      4001: 'Authentication token missing',
      4002: 'Invalid authentication token',
      4003: 'Token authentication error',
      4004: 'Chat ID missing',
      4005: 'Chat not found',
      4006: 'Access denied to chat',
      4007: 'Error getting chat details',
      1000: 'Normal closure',
      1001: 'Going away',
      1006: 'Connection lost'
    };
    return codeMessages[code] || `Connection closed with code ${code}`;
  }
  private scheduleReconnection(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.onError?.('Maximum reconnection attempts reached. Please refresh the page.');
      return;
    }

    // Clear any existing reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.reconnectAttempts++;
    
    // Exponential backoff with maximum delay cap
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    // Notify about reconnection attempt
    this.onConnectionStatusChange?.({
      isConnected: false,
      error: `Reconnecting... (Attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`,
      reconnectAttempts: this.reconnectAttempts
    });

    this.reconnectTimeout = setTimeout(() => {
      console.log(`Reconnecting now (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      this.connect();
    }, delay);
  }
  private handleConnectionError(message: string): void {
    console.error('WebSocket connection error:', message);
    
    // If the connection was previously established, try to reconnect
    if (this.isConnected) {
      this.isConnected = false;
      this.scheduleReconnection();
    }
    
    // Check if token might be expired based on the error message
    const tokenExpiredPattern = /token.*expired|invalid.*token/i;
    if (tokenExpiredPattern.test(message)) {
      message = "Authentication token expired. Please refresh the page to reconnect.";
      // Here you could implement token refresh logic
    }
    
    this.onError?.(message);
    this.onConnectionStatusChange?.({
      isConnected: false,
      error: message,
      reconnectAttempts: this.reconnectAttempts
    });
  }  private startPingInterval(): void {
    // Clear any existing interval
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    
    // Send a ping every 30 seconds
    this.pingInterval = setInterval(() => {
      if (this.isConnected && this.ws?.readyState === WebSocket.OPEN) {
        console.log('Sending ping to keep connection alive');
        this.sendMessage({ type: 'ping' } as WebSocketMessage);
        
        // If no pong response in 10 seconds, consider connection lost
        if (this.pingTimeoutRef) {
          clearTimeout(this.pingTimeoutRef);
        }
        
        this.pingTimeoutRef = setTimeout(() => {
          console.error('Ping timeout - no response received');
          this.handleConnectionError('Connection timed out');
          
          // Close and reconnect
          if (this.ws) {
            this.ws.close();
          }
          this.connect();
        }, 10000);
      }
    }, 30000); // Ping every 30 seconds
  }
  private stopPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
    
    if (this.pingTimeoutRef) {
      clearTimeout(this.pingTimeoutRef);
      this.pingTimeoutRef = null;
    }
  }  private sendMessage(message: WebSocketMessage): boolean {
    // Don't try to send if WebSocket is not initialized or not connected
    if (!this.ws) {
      console.log('WebSocket not initialized, queueing message:', message.type);
      this.messageQueue.push(message);
      
      // Try to connect if not already trying
      if (!this.reconnectTimeout) {
        this.connect();
      }
      return false;
    }
    
    // Check WebSocket state
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        // Connection is establishing, queue the message
        console.log('WebSocket connecting, queueing message:', message.type);
        this.messageQueue.push(message);
        return false;
        
      case WebSocket.OPEN:
        // Connection is open, send the message
        try {
          const serializedMessage = JSON.stringify(message);
          console.log('Sending WebSocket message:', serializedMessage);
          this.ws.send(serializedMessage);
          return true;
        } catch (error) {
          console.error('Error sending message:', error);
          // Queue message if there was an error sending
          this.messageQueue.push(message);
          this.handleConnectionError(`Error sending message: ${error instanceof Error ? error.message : 'Unknown error'}`);
          return false;
        }
        
      case WebSocket.CLOSING:
      case WebSocket.CLOSED:
        // Connection is closing or closed, queue the message and reconnect
        console.log('WebSocket closed or closing, queueing message:', message.type);
        this.messageQueue.push(message);
        
        // Try to reconnect if not already trying
        if (!this.reconnectTimeout && !this.isConnected) {
          this.connect();
        }
        return false;
        
      default:
        console.warn('Unknown WebSocket state:', this.ws.readyState);
        this.messageQueue.push(message);
        return false;
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }  public sendChatMessage(message: string): string | null {
    if (!message.trim()) {
      console.warn('Cannot send empty message');
      return null;
    }

    // Generate a client-side message ID for tracking
    const messageId = `client-msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      const messageData: WebSocketMessage = {
      type: 'chat.message',
      message: message.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      client_message_id: messageId  // Use the dedicated field for client tracking
    };

    console.log('Sending chat message:', messageData);
    this.trackAndSendMessage(messageData, messageId);
    
    // Return the message ID so the UI can track this message
    return messageId;
  }
    private trackAndSendMessage(message: WebSocketMessage, messageId?: string): void {
    const id = messageId || `msg-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // Add to pending messages for tracking
    this.pendingMessages.set(id, {
      id,
      message,
      timestamp: Date.now(),
      attempts: 1
    });
    
    // Set up retry mechanism
    this.startMessageRetryIfNeeded();
    
    // Send the message
    const sent = this.sendMessage(message);
    
    // Log for debugging
    if (sent) {
      console.log(`Message ${id} sent successfully, awaiting confirmation`);
    } else {
      console.log(`Message ${id} queued for sending`);
    }
  }
  
  private startMessageRetryIfNeeded(): void {
    // Don't create multiple retry intervals
    if (this.messageRetryTimeout) {
      return;
    }
    
    // Check for unsent messages every 5 seconds
    this.messageRetryTimeout = setInterval(() => {
      const now = Date.now();
      let hasRetried = false;
      
      this.pendingMessages.forEach((pending, id) => {
        // If message was sent more than 5 seconds ago and we haven't exceeded max retries
        if (now - pending.timestamp > 5000 && pending.attempts < 3) {
          console.log(`Retrying message ${id}, attempt ${pending.attempts + 1}`);
          
          // Update attempts and timestamp
          pending.attempts++;
          pending.timestamp = now;
          
          // Try sending again
          this.sendMessage(pending.message);
          hasRetried = true;
        } else if (pending.attempts >= 3) {
          // After 3 attempts, consider the message failed
          console.error(`Message ${id} failed after ${pending.attempts} attempts`);
          this.pendingMessages.delete(id);
          
          // Emit an error for the UI to handle
          this.onError?.(`Failed to send message after multiple attempts`);
        }
      });
      
      // If we have no pending messages, clear the interval
      if (this.pendingMessages.size === 0) {
        if (this.messageRetryTimeout) {
          clearInterval(this.messageRetryTimeout);
          this.messageRetryTimeout = null;
        }
      }
      
      // If we retried messages, update the UI
      if (hasRetried) {
        this.onConnectionStatusChange?.({
          isConnected: this.isConnected,
          reconnectAttempts: this.reconnectAttempts
        });
      }
    }, 5000);
  }  public sendTypingStart(): void {
    if (!this.isCurrentlyTyping) {
      this.isCurrentlyTyping = true;
      
      // Only send typing indicator if connected and chat ID is available
      if (this.isConnected && this.chatId) {
        const typingData: WebSocketMessage = {
          type: 'typing.start',
          chat_id: this.chatId,
          timestamp: new Date().toISOString()
        };
        
        // Low priority - don't track or retry typing indicators
        this.sendMessage(typingData);
      }
    }
  }

  public sendTypingStop(): void {
    if (this.isCurrentlyTyping) {
      this.isCurrentlyTyping = false;
      
      // Only send typing indicator if connected and chat ID is available
      if (this.isConnected && this.chatId) {
        const typingData: WebSocketMessage = {
          type: 'typing.stop',
          chat_id: this.chatId,
          timestamp: new Date().toISOString()
        };
        
        // Low priority - don't track or retry typing indicators
        this.sendMessage(typingData);
      }
    }
  }

  public handleUserTyping(): void {
    this.sendTypingStart();

    // Clear existing timeout
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }

    // Set timeout to stop typing after 3 seconds of inactivity
    this.typingTimeout = setTimeout(() => {
      this.sendTypingStop();
    }, 3000);
  }

  // Mark single message as read
  public markMessageAsRead(messageId: string): void {
    if (this.isConnected && this.chatId) {
      const readData: WebSocketMessage = {
        type: 'message.mark_read',
        message_id: messageId,
        chat_id: this.chatId,
        timestamp: new Date().toISOString()
      };

      console.log('Marking message as read:', messageId);
      this.sendMessage(readData);
    }
  }

  // Mark multiple messages as read
  public markMessagesAsRead(messageIds: string[]): void {
    if (this.isConnected && this.chatId && messageIds.length > 0) {
      const readData: WebSocketMessage = {
        type: 'messages.mark_read',
        message_ids: messageIds,
        chat_id: this.chatId,
        timestamp: new Date().toISOString()
      };

      console.log('Marking multiple messages as read:', messageIds);
      this.sendMessage(readData);
    }
  }  public disconnect(): void {
    this.stopPingInterval();
    
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
      this.typingTimeout = null;
    }
    
    if (this.pingTimeoutRef) {
      clearTimeout(this.pingTimeoutRef);
      this.pingTimeoutRef = null;
    }    if (this.messageRetryTimeout) {
      clearTimeout(this.messageRetryTimeout);
      this.messageRetryTimeout = null;
    }

    if (this.ws) {
      // Send a close message if possible before closing
      if (this.ws.readyState === WebSocket.OPEN) {
        try {
          // Let the server know this is an intentional closure
          this.ws.send(JSON.stringify({ type: 'client.disconnect' }));
        } catch (error) {
          console.warn('Error sending disconnect message:', error);
        }
      }
      
      this.ws.close(1000, 'User initiated disconnect');
      this.ws = null;
    }

    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.messageQueue = [];
    this.pendingMessages.clear();
    this.isCurrentlyTyping = false;
  }
  public getConnectionStatus(): ConnectionStatus {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    };
  }
  public updateChatId(newChatId: string, baseUrl?: string): void {
    this.chatId = newChatId;
    
    // Update the WebSocket URL using the same protocol detection logic as constructor
    const wsProtocol = typeof window !== 'undefined' && window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    
    if (baseUrl) {
      this.wsUrl = `${baseUrl}/ws/support/${newChatId}/?token=${encodeURIComponent(this.token)}`;
    } else if (typeof window !== 'undefined') {
      const wsBaseUrl = `${wsProtocol}//${window.location.host}`;
      this.wsUrl = `${wsBaseUrl}/ws/support/${newChatId}/?token=${encodeURIComponent(this.token)}`;
    } else {
      const wsBaseUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:8000';
      this.wsUrl = `${wsBaseUrl}/ws/support/${newChatId}/?token=${encodeURIComponent(this.token)}`;
    }
    
    console.log('Updated WebSocket URL:', this.wsUrl); // Debug log
    
    // If currently connected, disconnect and reconnect with new URL
    if (this.isConnected) {
      this.disconnect();
      setTimeout(() => this.connect(), 1000);
    }
  }
}
