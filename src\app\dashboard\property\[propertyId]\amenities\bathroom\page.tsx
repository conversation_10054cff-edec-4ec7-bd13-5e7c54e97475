'use client'

import HeaderPage from '@/components/_globals/headerPage'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import InputOnOff from '@/components/inputs/inputOnOff'
import Loader1 from '@/components/loaders/Loader1'
import { amenitiesCreateAndUpdate, amenitiesPropertyAmenities } from '@/services/api'
import { useRouter } from 'next/navigation'

import React, { useEffect, useState } from 'react'

interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({ params }: PageProps) {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [data, setData] = React.useState({
    available: false,
    bathrope: false,
    separated: false,
    bathtub: false,
    shower: false,
    soap: false,
    blower: false,
    shampoo: false
  })


  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      {
        name: 'Available',
        category: 'Bathroom',
        is_available: data.available ?? false,
      },
      {
        name: 'Bathrope',
        category: 'Bathroom',
        is_available: data.bathrope ?? false,
      },
      {
        name: 'Separated',
        category: 'Bathroom',
        is_available: data.separated ?? false,
      },
      {
        name: 'Bathtub',
        category: 'Bathroom',
        is_available: data.bathtub ?? false,
      },
      {
        name: 'Shower',
        category: 'Bathroom',
        is_available: data.shower ?? false,
      },
      {
        name: 'Soap',
        category: 'Bathroom',
        is_available: data.soap ?? false,
      },
      {
        name: 'Blower',
        category: 'Bathroom',
        is_available: data.blower ?? false,
      },
      {
        name: 'Shampoo',
        category: 'Bathroom',
        is_available: data.shampoo ?? false,
      },
    ];

    const call = await amenitiesCreateAndUpdate(params.propertyId, dataAmenities);

  };
  const handleFetchAmenities = async () => {
    if (!params.propertyId) return

     const call = await amenitiesPropertyAmenities(params.propertyId) as any

        if (call.status === 400){
          setIsLoading(false)
          return
        }
    
    
    
        
        // Update the data state based on the response
        const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) => index === 0 ? match.toLowerCase() : match.toUpperCase()) // Convert to camelCase
        .replace(/\s+/g, ''); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});


    setData(fetchedData);
    setIsLoading(false)
  }

  useEffect(() => {
    handleFetchAmenities()
  }, [])


  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title='Bagno'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />


      {
        isLoading ?
          <div className='h-full w-full flex items-center justify-center'>
            <Loader1 />
          </div>
          :
          <div className='mt-4 px-4 h-full w-full flex flex-col gap-3'>
            <InputOnOff
              title='Bagno Disponibile'
              value={data.available}
              onChange={(value) => {
                setData({
                  ...data,
                  available: value
                })

                handleUpdateAmenities({
                  ...data,
                  available: value
                })
              }}
            />

            <InputOnOff
              title='Accappatoio'
              value={data.bathrope}
              onChange={(value) => {
                setData({
                  ...data,
                  bathrope: value
                })
                handleUpdateAmenities({
                  ...data,
                  bathrope: value
                })

              }}
            />

            <InputOnOff
              title='Bagni Separati'
              value={data.separated}
              onChange={(value) => {
                setData({
                  ...data,
                  separated: value
                })
                handleUpdateAmenities({
                  ...data,
                  separated: value
                })
              }}
            />

            <InputOnOff
              title='Vasca'
              value={data.bathtub}
              onChange={(value) => {
                setData({
                  ...data,
                  bathtub: value
                })
                handleUpdateAmenities({
                  ...data,
                  bathtub: value
                })
              }}
            />

            <InputOnOff
              title='Doccia Separata'
              description='Non connesse alla vasca'
              value={data.shower}
              onChange={(value) => {
                setData({
                  ...data,
                  shower: value
                })
                handleUpdateAmenities({
                  ...data,
                  shower: value
                })
              }}
            />

            <InputOnOff
              title='Sapone & Carta Igenica'
              value={data.soap}
              onChange={(value) => {
                setData({
                  ...data,
                  soap: value
                })
                handleUpdateAmenities({
                  ...data,
                  soap: value
                })
              }}
            />

            <InputOnOff
              title='Asciugacapelli'
              value={data.blower}
              onChange={(value) => {
                setData({
                  ...data,
                  blower: value
                })
                handleUpdateAmenities({
                  ...data,
                  blower: value
                })
              }}
            />

            <InputOnOff
              title='Shampoo'
              value={data.shampoo}
              onChange={(value) => {
                setData({
                  ...data,
                  shampoo: value
                })
                handleUpdateAmenities({
                  ...data,
                  shampoo: value
                })
              }}
            />




          </div>
      }


    </MobilePageStart>
  )
}

export default Page



