import React, { useContext } from 'react'
import Header from './header'


interface HeaderProps {
  children?: React.ReactNode
}


function HeaderLogo({ children }: HeaderProps) {
  return (
    <Header>
      <div style={{
        display: 'flex',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
        height: '100%',
        width: '100%',
        paddingBottom: '10px',
        boxShadow: '0 0 10px rgba(0,0,0,0.1)',
        borderRadius: '0 0 20px 20px'
      }}>
        <img 
          src="./logos/platform.svg" 
          alt="" 
          style={{
            width: '120px',
            height: 'auto',
            margin: '0 auto',
            display: 'block'
          }}
        />
      </div>
    </Header>
  )
}

export default HeaderLogo