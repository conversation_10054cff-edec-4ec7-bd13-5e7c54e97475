'use client'

import React, { useEffect, useState } from 'react'
import style from './input.module.css'


/**
 *
 * @param props
 * @label the name of the input field
 * @value the value of the input field
 * @onChange the event triggered when inputing in the element
 * @type text || number
 * @required true || false
 * @rules {type: minLength || maxLength || email || regex, value: number || regex}
 * @returns
 */


function InputLabelDescription(
    props: {
        label?: string,
        description?: string,
        value: string | null | undefined,
        onChange: (e: any) => void,

        type?: string
        required?: boolean
        placeholder?: string
        style?: React.CSSProperties,
        rules?: { type: string, value?: any, message?: string }[]
        isTextArea?: boolean
        error?: string
        name?: string
        maxLength?: number
        icon?: React.ReactNode
        readOnly?: boolean
    }
) {
    const [error, setError] = useState('')

    const errorCheck = () => {
        if (props.rules) {
            if (props.value && props.value.length < 1) return

            for (const rule of props.rules) {

                if (props.required && !props.value) {
                    setError(`${props.label} is required`);
                    return;
                }

                if (rule.type === 'minLength' && props.value && props.value.length < rule.value) {
                    setError(`${props.label} must be at least ${rule.value} characters long`);
                    return;
                }

                if (rule.type === 'maxLength' && props.value && props.value.length >= rule.value) {
                    setError(`${props.label} must be no more than ${rule.value} characters long`);
                    return;
                }

                if (props.value && props.value.length >= 1 && rule.type === 'email' && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(props.value)) {
                    setError('Email format is invalid');
                    return;
                }

                if (rule.type === 'regex' && props.value && !rule.value.test(props.value)) {
                    if (rule.message) {
                        setError(rule.message);
                        return;
                    } else {
                        setError(`${props.label} is invalid`);
                        return;
                    }
                }
            }
        }
        setError(''); // Clear error if no rules are violated
    };

    const handleBlur = () => {
        document.querySelector("meta[name=viewport]")?.setAttribute(
            "content",
            "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        );
    };


    useEffect(() => {
        if (props.rules) {
            errorCheck()
        }
    }, [props.value])

    const sanitizedValue = props.value === null ? "" : props.value === undefined ? undefined : props.value;

    return (
        <div className={style.component}>
            <p>
                {
                    props.label &&
                    props.label
                }
                {
                    props.label &&
                    props.required &&
                    <label>*</label>
                }
            </p>

            {
                props.description &&
                <p className='text-gray-400' style={{ fontSize: '11px', marginTop: '0px' }}>
                   { props.description}
                </p>
            }

            <div className={`${props.isTextArea ? style.inputContainerArea : style.inputContainer}`} style={{ backgroundColor: 'white', marginTop: '10px' }}>
                {
                    !props.isTextArea ?
                        <div className="flex w-full">
                            <input
                                type={props.type ? props.type : 'text'}
                                placeholder={props.placeholder}
                                value={sanitizedValue}
                                onChange={(e) => {
                                    props.onChange(e.target.value)
                                }}
                                onBlur={handleBlur}
                                style={{
                                    fontSize: '16px',
                                    borderColor: props.error ? 'red' : undefined,
                                    flex: 1,
                                    backgroundColor: props.readOnly ? '#f5f5f5' : 'white'
                                }}
                                name={props.name}
                                readOnly={props.readOnly}
                            />
                            {props.icon}
                        </div>
                        :
                        <textarea
                            placeholder={props.placeholder}
                            value={sanitizedValue}
                            onChange={(e) => {
                                props.onChange(e.target.value)
                            }}
                            name={props.name}
                            readOnly={props.readOnly}
                            style={{
                                backgroundColor: props.readOnly ? '#f5f5f5' : 'white'
                            }}
                        />
                }
            </div>
            {
                props.error &&
                <p className="text-red-500 text-xs mt-1">
                    {props.error}
                </p>
            }
        </div>
    )
}

export default InputLabelDescription