import toast, { Toast } from "react-hot-toast";

interface ErrorToastProps {
  message: string;
  duration?: number;
  style?: React.CSSProperties;
}

/**
 * A custom toast for displaying error messages that may contain multiple lines
 * @param message The error message to display (can contain newlines)
 * @param duration Optional duration in milliseconds
 * @param style Optional custom styles
 */
export const showErrorToast = ({ message, duration = 5000, style = {} }: ErrorToastProps) => {
  // Split message by newlines to handle multi-line error messages
  const messageLines = message.split('\n');
  
  toast.custom(
    (t: Toast) => (
      <div
        className={`${
          t.visible ? 'animate-enter' : 'animate-leave'
        } max-w-md w-full shadow-lg rounded-lg pointer-events-auto flex ring-1 ring-black ring-opacity-5`}
        style={{
          backgroundColor: 'white',
          transform: `translateY(${t.visible ? '0' : '100%'})`,
          transition: 'transform 0.3s ease-in-out',
        }}
      >
        <div className="flex-1 w-0 p-4">
          <div className="flex items-start">
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-red-600">
                Errore
              </p>
              <div className="mt-1 text-sm text-gray-700">
                {messageLines.length === 1 ? (
                  <p>{message}</p>
                ) : (
                  <ul className="list-disc pl-4 space-y-1">
                    {messageLines.map((line, index) => (
                      <li key={index}>{line}</li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="flex border-l border-gray-200">
          <button
            onClick={() => toast.dismiss(t.id)}
            className="w-full border border-transparent rounded-none rounded-r-lg p-4 flex items-center justify-center text-sm font-medium text-gray-600 hover:text-gray-500 focus:outline-none"
          >
            Chiudi
          </button>
        </div>
      </div>
    ),
    {
      position: 'bottom-center',
      duration: duration,
      style: {
        bottom: '80px', // Position above the navbar
        zIndex: 20000, // Higher than navbar's z-index
        ...style
      },
    }
  );
};
