import React from 'react'
import styles from './input.module.css'

interface InputControllerProps {
    label: string,
    value: number,
    onChange: (value: number) => void
    maximum? : number
    minimum? : number
}

function InputCounter({
    label,
    value,
    onChange,
    maximum,
    minimum
}: InputControllerProps) {
    return (
        <div className={styles.inputCounter}>
            <p className='mb-2' style={{fontSize: '14px'}}>
                {
                    label &&
                    label
                }
            </p>

            <div className='bg-[white] px-4 py-3 rounded-[20px] w-full flex items-center justify-center gap-4'>
                <button onClick={()=>{
                        if (value <= 0) return
                        if (minimum && value <= minimum) return
                        onChange(value -1)
                }}
                style={{
                    backgroundColor: '#E5E5E5'
                }}
                >-</button>
                <p>{value}</p>
                <button onClick={()=>{
                    if (maximum && value >= maximum) return
                    onChange(value + 1)
                }}>+</button>
            </div>
        </div>
    )
}

export default InputCounter