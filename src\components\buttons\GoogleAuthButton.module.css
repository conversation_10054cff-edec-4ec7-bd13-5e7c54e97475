.googleButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 320px;
  height: 42px;
  background-color: white;
  border: 1px solid #dadce0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  color: #3c4043;
  font-family: '<PERSON>o', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 0;
  transition: all 0.2s ease;
  position: relative;
}

.googleButton:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
  background-color: #f8f9fa;
}

.googleButton:active {
  background-color: #f1f3f4;
}

.googleButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.googleIconWrapper {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.googleIcon {
  width: 100%;
  height: 100%;
}

.buttonText {
  padding-right: 12px;
  white-space: nowrap;
}

.spinner {
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #3c4043;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}