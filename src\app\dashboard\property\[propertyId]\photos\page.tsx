'use client'
import React, { useEffect, useRef, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import { useRouter } from 'next/navigation'
import { addPictureToProperty, deletePictureFromProperty, metadataViewPropertyData } from '@/services/api'
import toast from 'react-hot-toast'
import { AlertCircle, XIcon } from 'lucide-react'
import CardAlert from '@/components/cards/CardAlert'
import Loader1 from '@/components/loaders/Loader1'

interface FileError {
  file: string;
  error: string;
}

interface UploadError {
  message: string;
  errors: FileError[];
  failed_files: string[];
}

interface PropertyPhoto {
  id: string;
  image: string;
}

interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({params}: PageProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  const [pictures, setPictures] = useState<PropertyPhoto[]>([])
  const [selectedPictures, setSelectedPictures] = useState<File[]>([])
  const [uploadErrors, setUploadErrors] = useState<FileError[]>([])
  const [hasUploadError, setHasUploadError] = useState(false)

  const handleFetchMetadata = async () => {
    try {
      const call = await metadataViewPropertyData(params.propertyId)

      if (call.error){
        toast.error('Errore nel caricamento dei dati della proprietà')
      } else if (call[0]?.property?.photos) {
        setPictures(call[0].property.photos)
      } else {
        setPictures([])
      }
    } catch (error) {
      console.error('Error fetching property metadata:', error)
      toast.error('Errore nel caricamento dei dati della proprietà')
    } finally {
      setIsLoading(false)
    }
  }
  const handleUploadPhotos = async (files: any) => {
    setIsUploading(true)
    setHasUploadError(false)
    setUploadErrors([])

    try {
      const call = await addPictureToProperty(params.propertyId, files)

      if (call.status === 201) {
        toast.success('Foto caricate con successo')
        handleFetchMetadata()
      } else if (call.status === 400 && call.data) {
        // Handle validation errors
        const errorData = call.data as UploadError
        setUploadErrors(errorData.errors || [])
        setHasUploadError(true)
        toast.error(errorData.message || 'Errore nel caricamento delle foto')
      } else {
        toast.error('Errore nel caricamento delle foto')
      }
    } catch (error) {
      console.error('Error uploading photos:', error)
      toast.error('Si è verificato un errore durante il caricamento delle foto')
    } finally {
      setIsUploading(false)
    }
  }
  const handleDeletePhoto = async (photo_id: string) => {
    try {
      const call = await deletePictureFromProperty(photo_id)

      if (call.status === 200){
        toast.success('Foto cancellata con successo')
        // Refresh photos instead of full page reload
        handleFetchMetadata()
      } else {
        toast.error('Errore nella cancellazione delle foto')
      }
    } catch (error) {
      console.error('Error deleting photo:', error)
      toast.error('Si è verificato un errore durante la cancellazione della foto')
    }
  }

  useEffect(() => {
    handleFetchMetadata()
  }, [])



  return (
    <MobilePageStart>
      <HeaderPage title='Foto'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      {/* Main container with scrollable content */}
      <div className='mt-4 px-4 w-full flex flex-col gap-4'
        style={{
          height: 'calc(100vh - 60px)', /* Adjust height to account for header */
          overflowY: 'auto',
          paddingBottom: '20px'
        }}
      >
        {/* Guidelines section */}
        <div className='flex flex-col gap-2'>
          <CardAlert
            message='Cose da ricordare quando aggiungi le foto'
            color='orange'
          />
          <div>
            <p style={{ fontSize: '11px', color: '#********' }}>✅ Caricare le foto solo in formato JPEG</p>
            <p style={{ fontSize: '11px', color: '#********' }}>✅ Carica foto di alta qualità sotto i 10MB</p>
            <p style={{ fontSize: '11px', color: '#********' }}>✅ Carica almeno 25 foto per migliorare il ranking</p>
            <p style={{ fontSize: '11px', color: '#********' }}>✅ Massimo 45 foto</p>
          </div>
        </div>

        {/* Error alerts section */}
        {hasUploadError && uploadErrors.length > 0 && (
          <div className='flex flex-col gap-2'>
            <CardAlert
              title="Errore di caricamento"
              message="Alcuni file non sono stati caricati a causa di errori di validazione"
              color="red"
            />
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-[200px] overflow-y-auto">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-800">File non validi:</h4>
                  <ul className="mt-2 text-sm text-red-700 list-disc list-inside">
                    {uploadErrors.map((error, index) => (
                      <li key={index} className="mb-1">
                        <span className="font-medium">{error.file}:</span> {error.error}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* File input */}
        <div className='flex flex-col'>
          <input
            type="file"
            accept='image/jpeg'
            hidden
            multiple
            ref={inputRef}
            onChange={(e) => {
              if (e.target.files && e.target.files[0])
                handleUploadPhotos(e.target.files)
            }}
          />
        </div>

        {/* Photos display section */}
        {
          isLoading ?
          <div className='w-full py-8 flex items-center justify-center'>
            <Loader1/>
          </div>
          :
          <div className='flex flex-col gap-4'>
            <h3 className='text-sm font-medium text-gray-700'>Foto della proprietà</h3>

            {/* Horizontal scrollable container for photos */}
            <div className='w-full overflow-x-auto pb-2'>
              <div className='flex flex-row gap-3' style={{ minWidth: 'min-content' }}>
                {
                  pictures.length > 0 ?
                  pictures.map((p: PropertyPhoto) => (
                    <div key={p.id} className='relative flex-shrink-0'>
                      <div
                        onClick={() => handleDeletePhoto(p.id)}
                        className='w-[24px] h-[24px] bg-[tomato] rounded-full flex justify-center items-center cursor-pointer'
                        style={{ position: 'absolute', top: '8px', right: '8px', zIndex: 10 }}
                        aria-label="Delete photo"
                      >
                        <XIcon width={14} color="white"/>
                      </div>
                      <img
                        style={{ height: '200px', width: '320px', objectFit: 'cover', borderRadius: '10px' }}
                        src={p.image}
                        alt="Property photo"
                        loading="lazy"
                      />
                    </div>
                  )) : (
                    <div className="w-full text-center py-8 text-gray-500">
                      Nessuna foto caricata
                    </div>
                  )
                }
              </div>
            </div>

            {/* Upload button */}
            <div className='mt-2'>
              {
                !isUploading ?
                <Button
                  color='white'
                  backgroundColor='var(--blue)'
                  text='Carica foto'
                  fontSize='14px'
                  onClick={() => {
                    if (inputRef.current)
                      inputRef.current.click()
                  }}
                />
                :
                <div className='w-full flex items-center justify-center py-2'>
                  <Loader1/>
                </div>
              }
            </div>
          </div>
        }
      </div>
    </MobilePageStart>
  )
}

export default Page