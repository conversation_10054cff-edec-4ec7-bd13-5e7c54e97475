"use client"
import React, { createContext, useState, useEffect } from 'react';
import { getNotifications } from '@/services/api';

interface Notification {
    id: string;
    title?: string;
    message: string;
    is_read: boolean;
    created_at: string;
    read_at?: string;
}

interface NotificationsContextType {
    notifications: Notification[];
    unreadNotificationsCount: number;
    setNotifications: React.Dispatch<React.SetStateAction<Notification[]>>;
    refreshNotifications: () => Promise<void>;
    markNotificationAsRead: (notificationId: string) => void;
    isLoading: boolean;
}

export const NotificationsContext = createContext<NotificationsContextType>({
    notifications: [],
    unreadNotificationsCount: 0,
    setNotifications: () => {}, // Provide a default empty function
    refreshNotifications: async () => {},
    markNotificationAsRead: () => {},
    isLoading: false,
});

export const NotificationsProvider = ({ children }: { children: React.ReactNode }) => {
    const [notifications, setNotifications] = useState<Notification[]>([]);
    const [unreadNotificationsCount, setUnreadNotificationsCount] = useState<number>(0);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Function to fetch notifications from the API
    const refreshNotifications = async () => {
        setIsLoading(true);
        try {
            const call = await getNotifications();
            if (call && Array.isArray(call)) {
                setNotifications(call);
                const unreadCount = call.filter((notification: Notification) => !notification.is_read).length;
                setUnreadNotificationsCount(unreadCount);
            }
        } catch (error) {
            console.error('Error fetching notifications:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Initial fetch of notifications
    useEffect(() => {
        refreshNotifications();
    }, []);

    // Update unread count whenever notifications change
    useEffect(() => {
        const unreadCount = notifications.filter((notification) => !notification.is_read).length;
        setUnreadNotificationsCount(unreadCount);
    }, [notifications]);

    // Function to mark a notification as read locally (WebSocket will handle the server update)
    const markNotificationAsRead = (notificationId: string) => {
        setNotifications(prev =>
            prev.map(notification =>
                notification.id === notificationId
                    ? { ...notification, is_read: true, read_at: new Date().toISOString() }
                    : notification
            )
        );
    };

    return (
        <NotificationsContext.Provider value={{
            notifications,
            unreadNotificationsCount,
            setNotifications,
            refreshNotifications,
            markNotificationAsRead,
            isLoading
        }}>
            {children}
        </NotificationsContext.Provider>
    );
};