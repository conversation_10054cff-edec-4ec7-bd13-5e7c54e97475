import React from 'react'
import styles from './input.module.css'
import { ChevronDownIcon } from '@heroicons/react/24/outline'

interface InputSelectProps {
    label?: string
    placeholder: string
    options: {
        value: string
        label: string
        picture?: string
        recommended?: boolean
    }[]

    radiusImage?: string
    value: string
    name?: string
    error?: string
    onSelect: (value: string) => void

    style?: React.CSSProperties
    styleContainer?: React.CSSProperties
    readOnly?: boolean
}

function InputSelectLabel({ options, placeholder, onSelect, style, styleContainer, radiusImage, value, label, error, readOnly }: InputSelectProps) {
    const [selected, setSelected] = React.useState<string | null>(null)
    const [isOpen, setIsOpen] = React.useState(false)

    const toggleDropdown = () => {
        if (!readOnly) {
            setIsOpen(prev => !prev)
        }
    }
    const handleSelect = (event: any, value: string, label: string) => {
        event.stopPropagation()
        onSelect(value)
        setSelected(label)
        toggleDropdown()
    }

    return (
        <div className={styles.selectComponent} style={{marginBottom: '0px', ...styleContainer}}>
            {label && <p style={{ color: "black", fontSize: '14px', marginBottom: '5px' }}>{label}</p>}
            <div
                style={{
                    ...style,
                    background: readOnly ? '#f5f5f5' : 'white',
                    padding: '0 20px',
                    borderRadius: '13px',
                    borderColor: error ? 'red' : undefined,
                    cursor: readOnly ? 'default' : 'pointer'
                }}
                onClick={toggleDropdown}
                className={styles.containerSelect}
            >
                {value ? <div>{value}</div> : <div style={{color: '#ffffff80'}}>{placeholder}</div>}
                <ChevronDownIcon style={{ width: '20px' }} />
                {isOpen && (
                    <div
                        className={styles.dropdown}
                        onMouseLeave={toggleDropdown}
                        style={{...style, background: 'white'}}
                    >
                        {options.map((option, index) => (
                            <div
                                key={index}
                                onClick={(e) => handleSelect(e, option.value, option.label)}
                                className={styles.option}
                                style={{
                                    position: 'relative',
                                    paddingRight: option.recommended ? '120px' : undefined
                                }}
                            >
                                {option.picture && (
                                    <img
                                        src={option.picture}
                                        alt="flag"
                                        style={{
                                            width: '20px',
                                            height: '20px',
                                            marginRight: '10px',
                                            borderRadius: radiusImage ? radiusImage : '0px',
                                            objectFit: 'cover'
                                        }}
                                    />
                                )}
                                {option.label}
                                {option.recommended && (
                                    <span
                                        style={{
                                            position: 'absolute',
                                            right: '10px',
                                            background: '#4CAF50',
                                            color: 'white',
                                            padding: '2px 8px',
                                            borderRadius: '12px',
                                            fontSize: '12px'
                                        }}
                                    >
                                        scelta raccomandata
                                    </span>
                                )}
                            </div>
                        ))}
                    </div>
                )}
            </div>
            {error && (
                <p className="text-red-500 text-xs mt-1">
                    {error}
                </p>
            )}
        </div>
    )
}

export default InputSelectLabel