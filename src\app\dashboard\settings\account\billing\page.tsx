'use client'
import React, { useEffect, useRef, useState, useContext } from 'react'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'

import HeaderPage from '@/components/_globals/headerPage'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import Button from '@/components/buttons/Button'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import InputOnOff from '@/components/inputs/inputOnOff'
import InputCalendar from '@/components/inputs/InputCalendar'
import Loader1 from '@/components/loaders/Loader1'
import { createAddressBilling, createBilling, createTaxesBilling, getBilling, getBillingAddress, getBillingTaxes, updateAddressBilling, updateBilling, updateTaxesBilling } from '@/services/api'
import { UserContext } from '@/components/_context/UserContext'
import { LockIcon } from 'lucide-react'

// Country data for dropdown
const COUNTRIES = [
  { value: 'IT', label: 'Italia' },
  { value: 'FR', label: 'Francia' },
  { value: 'DE', label: 'Germania' },
  { value: 'ES', label: 'Spagna' },
  { value: 'UK', label: 'Regno Unito' },
  { value: 'US', label: 'Stati Uniti' },
  { value: 'CH', label: 'Svizzera' },
  { value: 'AT', label: 'Austria' },
  { value: 'BE', label: 'Belgio' },
  { value: 'NL', label: 'Paesi Bassi' },
  { value: 'PT', label: 'Portogallo' },
  { value: 'GR', label: 'Grecia' },
  { value: 'SE', label: 'Svezia' },
  { value: 'DK', label: 'Danimarca' },
  { value: 'FI', label: 'Finlandia' },
  { value: 'NO', label: 'Norvegia' },
  { value: 'PL', label: 'Polonia' },
  { value: 'CZ', label: 'Repubblica Ceca' },
  { value: 'HU', label: 'Ungheria' },
  { value: 'RO', label: 'Romania' },
  // Add more countries as needed
];

// IBAN validation patterns by country
const IBAN_PATTERNS: Record<string, { regex: RegExp, example: string, length: number }> = {
  'IT': { regex: /^IT\d{2}[A-Z]\d{10}[0-9A-Z]{12}$/, example: '***************************', length: 27 },
  'FR': { regex: /^FR\d{2}\d{10}[A-Z0-9]{11}\d{2}$/, example: '***************************', length: 27 },
  'DE': { regex: /^DE\d{2}\d{8}\d{10}$/, example: '**********************', length: 22 },
  'ES': { regex: /^ES\d{2}\d{8}\d{12}$/, example: '************************', length: 24 },
  'UK': { regex: /^GB\d{2}[A-Z]{4}\d{14}$/, example: '**********************', length: 22 },
  // Default pattern for other countries
  'default': { regex: /^[A-Z]{2}\d{2}[A-Z0-9]{10,30}$/, example: '**********************', length: 15 }
};

// Tax ID validation by country
const TAX_ID_VALIDATIONS: Record<string, { required: boolean, regex?: RegExp, name: string, example: string }> = {
  'IT': {
    required: true,
    regex: /^[A-Z]{6}\d{2}[A-Z]\d{2}[A-Z]\d{3}[A-Z]$/i,
    name: 'Codice Fiscale',
    example: '****************'
  },
  'FR': {
    required: true,
    regex: /^\d{13}$/,
    name: 'Numéro fiscal',
    example: '1234567890123'
  },
  'DE': {
    required: true,
    regex: /^\d{11}$/,
    name: 'Steueridentifikationsnummer',
    example: '12345678901'
  },
  'ES': {
    required: true,
    regex: /^[0-9A-Z][0-9]{7}[A-Z]$/i,
    name: 'NIF',
    example: '12345678Z'
  },
  'UK': {
    required: true,
    regex: /^\d{10}$/,
    name: 'UTR',
    example: '1234567890'
  },
  // Default for other countries
  'default': {
    required: false,
    name: 'Tax ID / VAT Number',
    example: 'Enter your tax identification number'
  }
};

// Postal code validation by country
const POSTAL_CODE_VALIDATIONS: Record<string, { regex: RegExp, example: string }> = {
  'IT': { regex: /^\d{5}$/, example: '20100' },
  'FR': { regex: /^\d{5}$/, example: '75001' },
  'DE': { regex: /^\d{5}$/, example: '10115' },
  'ES': { regex: /^\d{5}$/, example: '28001' },
  'UK': { regex: /^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/i, example: 'SW1A 1AA' },
  'US': { regex: /^\d{5}(-\d{4})?$/, example: '10001 or 10001-1234' },
  // Default for other countries
  'default': { regex: /^[A-Z0-9 -]{3,10}$/i, example: 'Postal code' }
};

function Page() {
  const router = useRouter()
  const inputRef = useRef<HTMLInputElement>(null)
  const { hasBillingProfile, isCustomer } = useContext(UserContext)
  const isReadOnly = hasBillingProfile && isCustomer

  const [isNew, setIsNew] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [name, setName] = useState('')
  const [surname, setSurname] = useState('')
  const [iban, setIban] = useState('')
  const [document, setDocument] = useState<File | null>(null)
  const [sex, setSex] = useState('')
  const [dateOfBirth, setDateOfBirth] = useState('')
  const [nationality, setNationality] = useState('IT') // Default to Italy
  const [address, setAddress] = useState('')
  const [city, setCity] = useState('')
  const [zipcode, setZipcode] = useState('')
  const [fiscalCode, setFiscalCode] = useState('')
  const [multiProperty, setMultiProperty] = useState(false)
  const [countryCode, setCountryCode] = useState('+39')
  const [country, setCountry] = useState('IT') // Country of residence (may differ from nationality)

  const [ids, setIds] = useState({
    billing: '',
    address: '',
    taxes: ''
  })

  // Extended validation errors
  const [errors, setErrors] = useState({
    name: '',
    surname: '',
    sex: '',
    dateOfBirth: '',
    nationality: '',
    document: '',
    address: '',
    city: '',
    zipcode: '',
    iban: '',
    fiscalCode: '',
    country: ''
  })

  // Clear a specific error when the corresponding field changes
  const clearError = (field: keyof typeof errors) => {
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' })
    }
  }

  // Get the appropriate tax ID field name based on country
  const getTaxIdFieldName = () => {
    return TAX_ID_VALIDATIONS[nationality]?.name || TAX_ID_VALIDATIONS['default'].name;
  }

  // Format IBAN with spaces for better readability
  const formatIban = (value: string) => {
    // Remove all spaces first
    const cleanValue = value.replace(/\s/g, '')
    // Add a space every 4 characters
    return cleanValue.replace(/(.{4})/g, '$1 ').trim()
  }

  const handleFetchUser = async () => {
    try {
      setIsLoading(true)
      const [billingResponse, addressResponse, taxesResponse] = await Promise.all([
        getBilling(),
        getBillingAddress(),
        getBillingTaxes()
      ])

      const billingData = billingResponse.data
      const addressData = addressResponse.data
      const taxesData = taxesResponse.data

      if (!billingData.length || !addressData.length || !taxesData.length) {
        setIsNew(true)
      } else {
        setName(billingData[0].first_name || '')
        setSurname(billingData[0].last_name || '')
        setSex(billingData[0].gender === 'male' ? 'M' : 'F')
        setDateOfBirth(billingData[0].date_of_birth || '')
        setNationality(billingData[0].nationality || 'IT')
        setIban(billingData[0].iban || '')

        setAddress(addressData[0].street_number || '')
        setCity(addressData[0].city || '')
        setZipcode(addressData[0].postcode || '')
        setCountry(addressData[0].country || 'IT')

        setFiscalCode(taxesData[0].vat_number || taxesData[0].tin_number || '')
        setMultiProperty(taxesData[0].rent_more_than_4_properties || false)

        setIds({
          billing: billingData[0].id,
          address: addressData[0].id,
          taxes: taxesData[0].id
        })
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
      toast.error('Errore nel caricamento dei dati. Riprova più tardi.')
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    let isValid = true
    const newErrors = { ...errors }

    // Name validation
    if (!name.trim()) {
      newErrors.name = 'Il nome è obbligatorio'
      isValid = false
    } else if (name.trim().length < 2) {
      newErrors.name = 'Il nome deve contenere almeno 2 caratteri'
      isValid = false
    } else {
      newErrors.name = ''
    }

    // Surname validation
    if (!surname.trim()) {
      newErrors.surname = 'Il cognome è obbligatorio'
      isValid = false
    } else if (surname.trim().length < 2) {
      newErrors.surname = 'Il cognome deve contenere almeno 2 caratteri'
      isValid = false
    } else {
      newErrors.surname = ''
    }

    // Sex validation
    if (!sex) {
      newErrors.sex = 'Il sesso è obbligatorio'
      isValid = false
    } else {
      newErrors.sex = ''
    }

    // Date of birth validation
    if (!dateOfBirth) {
      newErrors.dateOfBirth = 'La data di nascita è obbligatoria'
      isValid = false
    } else {
      const today = new Date()
      const birthDate = new Date(dateOfBirth)
      let age = today.getFullYear() - birthDate.getFullYear()
      const m = today.getMonth() - birthDate.getMonth()
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }
      if (age < 18) {
        newErrors.dateOfBirth = 'Devi avere almeno 18 anni'
        isValid = false
      } else if (birthDate > today) {
        newErrors.dateOfBirth = 'La data di nascita non può essere nel futuro'
        isValid = false
      } else if (age > 120) {
        newErrors.dateOfBirth = 'La data di nascita non è valida'
        isValid = false
      } else {
        newErrors.dateOfBirth = ''
      }
    }

    // Nationality validation
    if (!nationality) {
      newErrors.nationality = 'La nazionalità è obbligatoria'
      isValid = false
    } else {
      newErrors.nationality = ''
    }

    // Country validation
    if (!country) {
      newErrors.country = 'Il paese di residenza è obbligatorio'
      isValid = false
    } else {
      newErrors.country = ''
    }

    // Document validation (only for new records)
    if (isNew && !document) {
      newErrors.document = 'Il documento è obbligatorio'
      isValid = false
    } else {
      newErrors.document = ''
    }

    // Address validation
    if (!address.trim()) {
      newErrors.address = 'L\'indirizzo è obbligatorio'
      isValid = false
    } else if (address.trim().length < 5) {
      newErrors.address = 'Inserisci un indirizzo completo'
      isValid = false
    } else {
      newErrors.address = ''
    }

    // City validation
    if (!city.trim()) {
      newErrors.city = 'La città è obbligatoria'
      isValid = false
    } else if (city.trim().length < 2) {
      newErrors.city = 'Il nome della città non è valido'
      isValid = false
    } else {
      newErrors.city = ''
    }

    // Zipcode/Postal code validation based on country
    if (!zipcode.trim()) {
      newErrors.zipcode = 'Il CAP è obbligatorio'
      isValid = false
    } else {
      const postalValidation = POSTAL_CODE_VALIDATIONS[country] || POSTAL_CODE_VALIDATIONS['default']
      if (!postalValidation.regex.test(zipcode.trim())) {
        newErrors.zipcode = `Formato CAP non valido (es. ${postalValidation.example})`
        isValid = false
      } else {
        newErrors.zipcode = ''
      }
    }

    // IBAN validation based on country
    if (!iban.trim()) {
      newErrors.iban = 'L\'IBAN è obbligatorio'
      isValid = false
    } else {
      // Remove spaces for validation
      const cleanIban = iban.replace(/\s/g, '')
      const countryPattern = IBAN_PATTERNS[country] || IBAN_PATTERNS['default']

      // First check if the IBAN starts with the right country code
      if (!cleanIban.startsWith(country)) {
        newErrors.iban = `L'IBAN deve iniziare con ${country}`
        isValid = false
      }
      // Then check the pattern
      else if (!countryPattern.regex.test(cleanIban)) {
        newErrors.iban = `Formato IBAN non valido (es. ${countryPattern.example})`
        isValid = false
      } else {
        newErrors.iban = ''
      }
    }

    // Tax ID validation based on nationality
    const taxValidation = TAX_ID_VALIDATIONS[nationality] || TAX_ID_VALIDATIONS['default']
    if (taxValidation.required && !fiscalCode.trim()) {
      newErrors.fiscalCode = `${taxValidation.name} è obbligatorio`
      isValid = false
    } else if (taxValidation.required && taxValidation.regex && !taxValidation.regex.test(fiscalCode.trim())) {
      newErrors.fiscalCode = `Formato ${taxValidation.name} non valido (es. ${taxValidation.example})`
      isValid = false
    } else {
      newErrors.fiscalCode = ''
    }

    setErrors(newErrors)
    return isValid
  }

  const handleCreateBillingData = async () => {
    if (!validateForm()) {
      // Scroll to first error
      const firstErrorField = Object.keys(errors).find(key => errors[key as keyof typeof errors] !== '')
      if (firstErrorField && typeof window !== 'undefined') {
        const element = window.document.querySelector(`[name="${firstErrorField}"]`)
        element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      return
    }

    try {
      setIsSubmitting(true)

      // Check for required document
      if (isNew) {
        if (!document) {
          throw new Error('Il documento è obbligatorio')
        }
      }

      // Create billing first
      const billingResponse = await createBilling(
        name.trim(),
        surname.trim(),
        dateOfBirth,
        nationality,
        'NP',
        sex === 'M' ? 'male' : 'female',
        document!, // We can safely assert non-null here due to check above
        iban.replace(/\s/g, '')
      )

      if (!billingResponse.data) {
        throw new Error('Errore nella creazione dei dati di fatturazione')
      }

      // Only proceed with address and taxes if billing succeeds
      const [addressResponse, taxesResponse] = await Promise.all([
        createAddressBilling(
          address.trim(),
          zipcode.trim(),
          city.trim(),
          country
        ),
        createTaxesBilling(
          fiscalCode.trim().toUpperCase(),
          nationality,
          multiProperty
        )
      ])
      // Handle potential errors in responses
      if (!billingResponse.data || !addressResponse.data || !taxesResponse.data) {
        throw new Error('Errore nella creazione dei dati di fatturazione')
      }

      toast.success('Dettagli fatturazione creati con successo')
      router.push('/dashboard/settings/account/')
    } catch (error) {
      console.error('Error creating billing data:', error)
      toast.error('Errore nella creazione dei dati di fatturazione. Riprova più tardi.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleUpdateBillingData = async () => {
    if (!validateForm()) {
      // Scroll to first error
      const firstErrorField = Object.keys(errors).find(key => errors[key as keyof typeof errors] !== '')
      if (firstErrorField && typeof window !== 'undefined') {
        const element = window.document.querySelector(`[name="${firstErrorField}"]`)
        element?.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      return
    }

    try {
      setIsSubmitting(true)

      // Make API calls in parallel for better performance
      const [billingResponse, addressResponse, taxesResponse] = await Promise.all([
        updateBilling({
          name: name.trim(),
          surname: surname.trim(),
          date_of_birth: dateOfBirth,
          nationality: nationality,
          gender: sex === 'M' ? 'male' : 'female',
          billingProfileId: ids.billing,
          iban: iban.replace(/\s/g, '')
        }),
        updateAddressBilling(
          address.trim(),
          zipcode.trim(),
          city.trim(),
          country,
          ids.address,
        ),
        updateTaxesBilling(
          fiscalCode.trim().toUpperCase(),
          nationality,
          multiProperty,
          ids.taxes
        )
      ])

      // Handle potential errors in responses
      if (!billingResponse.data || !addressResponse.data || !taxesResponse.data) {
        throw new Error('Errore nell\'aggiornamento dei dati di fatturazione')
      }

      toast.success('Dettagli fatturazione aggiornati con successo')
      router.push('/dashboard/settings/account/')
    } catch (error) {
      console.error('Error updating billing data:', error)
      toast.error('Errore nell\'aggiornamento dei dati di fatturazione. Riprova più tardi.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const getMaxDate = () => {
    const today = new Date()
    today.setFullYear(today.getFullYear() - 18)
    return today.toISOString().split('T')[0]
  }

  // Handle nationality change - update tax ID requirements
  const handleNationalityChange = (value: string) => {
    setNationality(value)
    clearError('nationality')
    clearError('fiscalCode')

    // Reset fiscal code if country changes to avoid validation errors
    if (value !== nationality) {
      setFiscalCode('')
    }
  }

  // Handle country change - update IBAN requirements
  const handleCountryChange = (value: string) => {
    setCountry(value)
    clearError('country')
    clearError('iban')
    clearError('zipcode')

    // Reset IBAN if country changes to avoid validation errors
    if (value !== country) {
      setIban('')
    }
  }

  useEffect(() => {
    handleFetchUser()
  }, [])

  return (
    <MobilePageStart
      isNavbar
    >
      <HeaderPage
        title='Fatturazione'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      {isLoading ? (
        <div className='h-full w-full flex items-center justify-center'>
          <Loader1 />
        </div>
      ) : (
        <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>
          <div className='flex flex-col gap-4'>
            <h2 className='text-lg font-semibold'>Informazioni personali</h2>

            <InputLabelDescription
              label='Nome'
              placeholder='Mario'
              value={name}
              onChange={(value) => {
                setName(value)
                clearError('name')
              }}
              error={errors.name}
              name="name"
              readOnly={isReadOnly}
            />
            <InputLabelDescription
              label='Cognome'
              placeholder='Rossi'
              value={surname}
              onChange={(value) => {
                setSurname(value)
                clearError('surname')
              }}
              error={errors.surname}
              name="surname"
              readOnly={isReadOnly}
            />
            <InputSelectLabel
              label='Sesso'
              placeholder='Seleziona'
              value={sex}
              onSelect={(value) => {
                setSex(value)
                clearError('sex')
              }}
              options={[
                { value: 'M', label: 'Uomo' },
                { value: 'F', label: 'Donna' },
              ]}
              error={errors.sex}
              name="sex"
              readOnly={isReadOnly}
            />
            <InputCalendar
              label='Data di nascita'
              placeholder='Seleziona'
              value={dateOfBirth}
              onChange={(date) => {
                setDateOfBirth(date)
                clearError('dateOfBirth')
              }}
              max={getMaxDate()}
              error={errors.dateOfBirth}
              name="dateOfBirth"
              readOnly={isReadOnly}
            />
            <InputSelectLabel
              label='Nazionalità'
              placeholder='Seleziona'
              value={nationality}
              onSelect={handleNationalityChange}
              options={COUNTRIES}
              error={errors.nationality}
              name="nationality"
              readOnly={isReadOnly}
            />

            {isNew && (
              <div className='flex flex-col gap-4'>
                <input
                  type="file"
                  accept='image/jpeg,image/png,application/pdf'
                  hidden
                  ref={inputRef}
                  onChange={(e) => {
                    if (e.target.files && e.target.files[0]) {
                      const file = e.target.files[0]
                      // Validate file size (max 5MB)
                      if (file.size > 5 * 1024 * 1024) {
                        toast.error('Il file deve essere inferiore a 5MB')
                        return
                      }
                      setDocument(file)
                      clearError('document')
                    }
                  }}
                  name="document"
                />
                <div className='flex flex-col gap-2'>
                  <p className='text-[13px]'>Documento di identità</p>
                  <p className='text-[11px] text-gray-500'>Carica un documento di identità valido (carta d'identità, passaporto, patente)</p>
                  {errors.document && <p className='text-red-500 text-xs'>{errors.document}</p>}
                </div>
                {document && (
                  <div className='flex flex-col gap-2'>
                    <img
                      src={URL.createObjectURL(document)}
                      className='w-full rounded-lg'
                      alt="Documento caricato"
                    />
                    <Button
                      color='white'
                      backgroundColor='var(--red)'
                      text='Rimuovi Documento'
                      fontSize='14px'
                      onClick={() => setDocument(null)}
                    />
                  </div>
                )}
                {!document && (
                  <Button
                    color='white'
                    backgroundColor='var(--blue)'
                    text='Carica Documento'
                    fontSize='14px'
                    onClick={() => {
                      if (inputRef.current)
                        inputRef.current.click()
                    }}
                  />
                )}
              </div>
            )}

            <h2 className='text-lg font-semibold mt-4'>Indirizzo</h2>

            <InputSelectLabel
              label='Paese di residenza'
              placeholder='Seleziona'
              value={country}
              onSelect={handleCountryChange}
              options={COUNTRIES}
              error={errors.country}
              name="country"
              readOnly={isReadOnly}
            />

            <InputLabelDescription
              label='Indirizzo'
              placeholder='Via Roma 123'
              value={address}
              onChange={(value) => {
                setAddress(value)
                clearError('address')
              }}
              error={errors.address}
              name="address"
              readOnly={isReadOnly}
            />
            <InputLabelDescription
              label='Città'
              placeholder='Milano'
              value={city}
              onChange={(value) => {
                setCity(value)
                clearError('city')
              }}
              error={errors.city}
              name="city"
              readOnly={isReadOnly}
            />
            <InputLabelDescription
              label={country === 'IT' ? 'CAP' : 'Codice Postale'}
              placeholder={POSTAL_CODE_VALIDATIONS[country]?.example || POSTAL_CODE_VALIDATIONS['default'].example}
              value={zipcode}
              onChange={(value) => {
                // For Italy and most European countries, allow only digits with max length of 5
                if (['IT', 'FR', 'DE', 'ES'].includes(country)) {
                  const onlyDigits = value.replace(/\D/g, '')
                  setZipcode(onlyDigits)
                } else {
                  // For other countries, allow more flexible format
                  setZipcode(value)
                }
                clearError('zipcode')
              }}
              maxLength={country === 'UK' ? 8 : (country === 'CA' ? 7 : 10)}
              error={errors.zipcode}
              name="zipcode"
              readOnly={isReadOnly}
            />

            <h2 className='text-lg font-semibold mt-4'>Informazioni fiscali</h2>

            <InputLabelDescription
              label={getTaxIdFieldName()}
              placeholder={TAX_ID_VALIDATIONS[nationality]?.example || TAX_ID_VALIDATIONS['default'].example}
              value={fiscalCode}
              onChange={(value) => {
                // Auto-uppercase for fiscal code if Italian
                if (nationality === 'IT') {
                  setFiscalCode(value.toUpperCase())
                } else {
                  setFiscalCode(value)
                }
                clearError('fiscalCode')
              }}
              maxLength={nationality === 'IT' ? 16 : 20}
              error={errors.fiscalCode}
              name="fiscalCode"
              readOnly={isReadOnly}
            />

            <InputLabelDescription
              label='IBAN'
              placeholder={IBAN_PATTERNS[country]?.example || IBAN_PATTERNS['default'].example}
              value={iban}
              onChange={(value) => {
                // Format IBAN with spaces
                setIban(formatIban(value))
                clearError('iban')
              }}
              error={errors.iban}
              name="iban"
              readOnly={isReadOnly}
            />
            {country === 'IT' && !isReadOnly && (
              <InputOnOff
                title='Multiproprieta in Italia'
                description='Affitti più di 4 immobili in Italia per periodi brevi?'
                value={multiProperty}
                onChange={(value) => {
                  setMultiProperty(value)
                }}
              />
            )}
          </div>

          {/* Help text for requirements */}
          <div className="p-4 bg-blue-50 rounded-lg text-sm text-gray-700 mb-4">
            <p className="font-semibold mb-1">Informazioni importanti:</p>
            <ul className="list-disc pl-4 text-xs space-y-1">
              <li>Tutti i campi di input sono obbligatori</li>
              <li>L'IBAN deve essere valido per il paese di residenza selezionato</li>
              {nationality === 'IT' && (
                <li>Il codice fiscale deve essere correttamente formattato (16 caratteri)</li>
              )}
              {country === 'IT' && (
                <li>Se affitti più di 4 immobili in Italia, sei soggetto a regolamentazioni fiscali specifiche</li>
              )}
            </ul>
          </div>

          {isReadOnly ? (
            <div className="flex flex-col items-center mt-4 mb-4">
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                <LockIcon size={24} className="text-[#113158]" />
              </div>
              <p className="text-center text-gray-700 text-sm">
                I dati di fatturazione non possono essere modificati perché hai già firmato il contratto.
                Per assistenza, contatta il nostro team di supporto.
              </p>
              <div style={{ marginTop: '20px' }}>
                <Button
                  color='var(--blue)'
                  backgroundColor='transparent'
                  text='Torna Indietro'
                  fontSize='14px'
                  border='1px solid var(--blue)'
                  onClick={() => router.back()}
                />
              </div>
            </div>
          ) : (
            <>
              <Button
                color='white'
                backgroundColor='var(--blue)'
                text={isSubmitting ? 'Salvataggio in corso...' : 'Salva'}
                fontSize='14px'
                disabled={isSubmitting}
                onClick={() => {
                  isNew ? handleCreateBillingData() : handleUpdateBillingData()
                }}
              />

              <Button
                color='var(--blue)'
                backgroundColor='transparent'
                text='Annulla'
                fontSize='14px'
                border='1px solid var(--blue)'
                disabled={isSubmitting}
                onClick={() => router.back()}
              />
            </>
          )}
          <br />
        </div>
      )}
    </MobilePageStart>
  )
}

export default Page