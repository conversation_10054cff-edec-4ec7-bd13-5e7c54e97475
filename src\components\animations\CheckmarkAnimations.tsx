import { useEffect, useState } from "react";
import dynamic from "next/dynamic";

// Cargar Lottie dinámicamente para evitar SSR
const Lottie = dynamic(() => import("lottie-react"), { ssr: false });

import animation from "../../../public/animations/checkmark.json";

interface Animation {
  onComplete: () => void;
}

const CheckmarkAnimation = ({ onComplete }: Animation) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) return null; // Evita renderizar en el servidor

  return (
    <div style={{ width: "100px", height: "100px" }}>
      <Lottie
        animationData={animation}
        style={{ height: "100%", width: "100%" }}
        loop={true}
        onLoopComplete={() => {
          onComplete();
        }}
      />
    </div>
  );
};

export default CheckmarkAnimation;
