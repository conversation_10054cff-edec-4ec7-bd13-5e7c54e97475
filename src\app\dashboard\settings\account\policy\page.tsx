'use client'
import React, { useEffect, useState } from 'react'
import MobilePage from '@/components/_globals/mobilePage'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import MultiSelect from '@/components/inputs/multiSelect'
import Button from '@/components/buttons/Button'
import InputCounter from '@/components/inputs/inputCounter'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import Link from 'next/link'
import ButtonSection from '@/components/buttons/ButtonSection'


function Page() {
  const router = useRouter()


  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [repeatPassword, setRepeatPassword] = useState('')





  return (
    <MobilePageStart>
      <HeaderPage title='Account Policy'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard/settings`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-4'>
        <div className='flex flex-col gap-2 w-full'>
          <h4 className='text-[14px]' style={{ fontWeight: '500', marginBottom: '6px' }}>Informazioni Personali</h4>
          <ButtonSection
            completed={true}
            title='Richiedi Modifica Policy'
            desciption=' 
            Se hai bisogno di modificare la tua policy, clicca qui'
            href={`/dashboard/settings/account/policy/modify`}
          />
          <ButtonSection
            completed={true}
            title='Richiedi Cancellazione Proprietà'
            desciption='
            Se hai bisogno di cancellare una proprietà, clicca qui'
            href={`/dashboard/settings/account/policy/deleteprop`}
          />
          <ButtonSection
            completed={true}
            title='Richiedi Cancellazione Account'
            desciption='
            Se hai bisogno di cancellare il tuo account, clicca qui'
            href={`/dashboard/settings/account/policy/deleteacc`}
          />

        </div>
      </div>


    </MobilePageStart>
  )
}

export default Page