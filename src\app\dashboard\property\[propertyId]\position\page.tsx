'use client'
import React, { useContext, useEffect, useState } from 'react'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import dynamic from 'next/dynamic'
import toast from 'react-hot-toast'
import Card<PERSON>lert from '@/components/cards/CardAlert'
import { locationGetLocation, locationUpdateLocation } from '@/services/api'
import ButtonLoading from '@/components/buttons/ButtonLoading'
import { PropertyContext } from '@/components/_context/PropertyContext'
const InputMapNoSSR = dynamic(() => import('@/components/inputs/inputCardMap'), { ssr: false });

interface PageProps {
  params: {
    propertyId: string
  }
}

interface Property {
  id: string;
  location: string
  // Otras propiedades...
}

function Page({ params }: PageProps) {
  const router = useRouter()
  const { properties } = useContext(PropertyContext);
  

  const [city, setCity] = useState('')
  const [zip, setZip] = useState('')
  const [street, setStreet] = useState('')
  const [isEditable, setIsEditable] = useState(false)

  const [cords, setCords] = useState({
    lat: 0.000,
    lng: 0.000
  })

  const [isMapOpen, setIsMapOpen] = useState(false)

  const handleFetchData = async () => {
    //Check if is multiproperty
    const property = properties.find((property: any) => property.id === params.propertyId)
    const location = property?.location ?? null;

    if (!location){
      return router.push('/dashboard')
    }



    const call = await locationGetLocation(location)
    setCity(call.city)
    setZip(call.post_code)
    setStreet(call.street)
    setIsEditable(call.is_editable)

  }

  const handleFetchCordinates = async () => {

    if (street === '' || zip === '' || city === '') {
      return toast.error('Tutti i campi sono obbligatori')
    }

    const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=${street},${city}${zip}&key=${process.env.NEXT_PUBLIC_GOOGLE_API_KEY}`, {
      method: "GET"
    })

    const data = await response.json()
    if (data.status != 'OK') {
      return toast.error('Indirizzo non trovato')
    }

    if (data.results.length > 0) {
      const { location } = data.results[0].geometry

      setCords({
        lat: location.lat,
        lng: location.lng
      })

      setIsMapOpen(true)
    }



  }
  const handleUpdatePosition = async () => {
    const property = properties.find((property: any) => property.id == params.propertyId)

    if (!property) return
    const { location } = property




    const call = await locationUpdateLocation(location, street, zip, city, 'Italy', cords.lat, cords.lng)

    toast.success('Posizione aggiornata con successo')
    return true

  }

  useEffect(()=>{
    handleFetchData()
  },[])



  return (
    <MobilePageStart>
      <HeaderPage title='Posizione'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      {
        !isEditable &&
        <div className='mt-4 px-4 w-full flex flex-col justify-between'>
          <CardAlert
            title='Attenzione'
            message='Per effettuare una modifica alla posizione è necessario contattare il '
            learnMoreText='Supporto Clienti'
            learnMoreLink='/dashboard/chat'
            color='red'
          />
        </div>
      }

      {
        !isMapOpen &&
        <div className='mt-4 px-4 h-full w-full flex flex-col justify-between'>
          <div className='flex flex-col gap-4'>
            <InputLabelDescription
              label='In che città si trova?'
              placeholder='Es. Milano'
              value={city}
              onChange={setCity}
            />

            <InputLabelDescription
              label='Codice postale'
              placeholder='Es. 21300'
              value={zip}
              onChange={setZip}
            />

            <InputLabelDescription
              label='Indirizzo'
              placeholder='Es. Via della spiga'
              value={street}
              onChange={setStreet}
            />

            <ButtonLoading
              color='white'
              backgroundColor='var(--blue)'
              text='Continua'
              deactive={!isEditable}
              onClick={() => {
               return handleFetchCordinates()
              }}
              
            />
          </div>
        </div>
      }

      {
        isMapOpen &&
        <div className='mt-4 px-4 h-full w-full flex flex-col justify-start'>
          <CardAlert
            title='Attenzione'
            message='Controlla sulla mappa se la posizione trovata corrisponde a quella della proprietà'
            color='green'
            style={{
              marginBottom: '20px'
            }}
          />
          <InputMapNoSSR
            style={{
              background: 'none'
            }}
            position={{
              lat: cords.lat.toString(),
              lng: cords.lng.toString()
            }}
          />
          <br />
          <ButtonLoading
            color='white'
            backgroundColor='var(--blue)'
            text='Continua'
            deactive={!isEditable}
            onClick={() => {
            return  handleUpdatePosition()
            }}
            
          />
        </div>
      }


    </MobilePageStart>
  )
}

export default Page