.header {
    position: absolute;
    top: 0;


    background-color: var(--background);

    width: 100%;
    min-height: 60px;
    height: 60px;


    border-radius: 0 0 20px 20px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 10000;


    .dropDown_title_box {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 15px;
    }

    .header_icon {
        margin-right: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .profilePicture {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--card-background);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
}
.modal {
    cursor: default;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 30000;
    backdrop-filter: blur(5px);
    user-select: none;

    .containerElements {
        position: relative;
        z-index: 1000;
        transition: all 0.3s;

        max-height: 90%;
        padding: 20px;

        background-color: var(--card-background);
        display: flex;
        flex-direction: column;

        box-shadow:
            0 0 0 1px hsla(0, 0%, 100%, 0.08),
            0 2px 4px rgba(0, 0, 0, 0.3),
            0 12px 24px rgba(0, 0, 0, 0.3);

        align-items: center;
        justify-content: flex-start;
        overflow-y: scroll;

        border-radius: 20px;
    }
}
.mobilePage {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}
.mobilePageStart {
    display: flex;
    flex-direction: column;

    justify-content: flex-start;
    align-items: flex-start;
    height: 100dvh;
    width: 100%;


    padding-top: 60px;
    padding-bottom: 60px;

    background-color: var(--card-background);
    scrollbar-color: 'transparent transparent';
    scrollbar-width: 0px;


    overflow-y: scroll;

}
.navbar {
    height:70px;
    width: 100%;
    background-color: var(--background);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    position: absolute;
    bottom: 0px;

    padding: 0 20px;
    padding-bottom:0px;
    display: flex;
    justify-content: space-between;
    z-index: 10000; /* Keep this z-index lower than the toast z-index (20000) */
    padding-bottom: 10px;

}
