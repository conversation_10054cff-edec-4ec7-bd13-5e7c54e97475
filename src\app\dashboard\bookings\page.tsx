'use client'
import React, { useContext, useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderLogo from '@/components/_globals/headerLogo'
import Navbar from '@/components/_globals/navbar'
import { useRouter } from 'next/navigation'
import { getAllBookings, propertyAllProperties } from '@/services/api'
import toast from 'react-hot-toast'
import Loader1 from '@/components/loaders/Loader1'
import { Calendar, Clock, User, Home, Filter, Laptop, ClipboardList } from 'lucide-react'

const statusConfig = {
  "new": { color: '#2196F3', bgColor: '#E3F2FD', icon: 'sparkles', label: 'Nuova' },
  "modified": { color: '#FF9800', bgColor: '#FFF3E0', icon: 'pencil', label: 'Modificata' },
  "request": { color: '#9C27B0', bgColor: '#F3E5F5', icon: 'clock', label: 'Richie<PERSON>' },
  "cancelled": { color: '#F44336', bgColor: '#FFEBEE', icon: 'x', label: 'Cancellata' },
  "completed": { color: '#4CAF50', bgColor: '#E8F5E9', icon: 'check', label: 'Completata' }
} as const;

function Page() {
  const router = useRouter()
  const [bookings, setBookings] = useState<any>([])
  const [filteredBookings, setFilteredBookings] = useState<any>([])
  const [selectedBooking, setSelectedBooking] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeFilter, setActiveFilter] = useState<string | null>(null)
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalBookings, setTotalBookings] = useState(0)

  const handleFetchAllBookings = async (page: number = 1) => {
    try {
      setIsLoading(true)
      const properties = await propertyAllProperties()
      const call = await getAllBookings(page, pageSize)

      // Update pagination state
      setCurrentPage(call.currentPage)
      setTotalPages(call.totalPages)
      setTotalBookings(call.count)

      /* Put property data along with the reservation */
      const mappedBookings = call.results.map((booking: any) => {
        return {
          id: booking.id,
          property: properties.find((property: any) => property.id === booking.property),
          customer: booking.customer,
          reservation_data: booking.reservation_data,
          status: booking.status,
          is_manual: booking.is_manual,
        }
      })

      setBookings(mappedBookings)
      setFilteredBookings(mappedBookings)
    } catch (error) {
      console.error('Error fetching bookings:', error)
      toast.error('Errore nel caricamento delle prenotazioni')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelectBooking = (booking: any) => {
    localStorage.setItem('selectedBooking-heibooky', JSON.stringify(booking))
    router.push('/dashboard/bookings/details')
  }

  const handleFilterChange = (status: string | null) => {
    // Reset to first page when changing filters
    if (currentPage !== 1) {
      setCurrentPage(1)
    }

    if (status === activeFilter) {
      // If clicking the same filter, remove it
      setActiveFilter(null)
      setFilteredBookings(bookings)
    } else {
      // Apply the new filter
      setActiveFilter(status)
      if (status) {
        const filtered = bookings.filter((booking: any) => booking.status === status)
        setFilteredBookings(filtered)
      } else {
        setFilteredBookings(bookings)
      }
    }
    // Hide filter menu after selection
    setShowFilters(false)
  }

  useEffect(() => {
    handleFetchAllBookings(currentPage)
  }, [currentPage, pageSize])

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusConfig = (status: string) => {
    return statusConfig[status as keyof typeof statusConfig] ||
      { color: '#999', bgColor: '#f0f0f0', icon: 'circle', label: 'Sconosciuto' };
  };

  const renderStatusIcon = (status: string) => {
    switch (status) {
      case 'new': return '✨';
      case 'modified': return '✏️';
      case 'request': return '⏰';
      case 'cancelled': return '❌';
      case 'completed': return '✓';
      default: return '⚪';
    }
  };

  return (
    <MobilePageStart isNavbar>
      <HeaderLogo />
      <div className='w-full h-full flex flex-col p-4 gap-4 pb-20 overflow-auto bg-gray-50'>
        <div className='flex justify-between items-center mb-2'>
          <h1 className='text-xl font-bold text-[#133157]'>Le tue prenotazioni</h1>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className='flex items-center gap-1 p-2 rounded-lg text-[#133157] hover:bg-gray-100'
          >
            <Filter className='w-5 h-5' />
            <span className='text-sm font-medium'>Filtri</span>
          </button>
        </div>

        {showFilters && (
          <div className='bg-white rounded-lg shadow-md p-3 mb-4'>
            <h3 className='text-sm font-medium text-gray-700 mb-2'>Filtra per stato:</h3>
            <div className='flex flex-wrap gap-2'>
              {Object.entries(statusConfig).map(([key, config]) => (
                <button
                  key={key}
                  onClick={() => handleFilterChange(key)}
                  className='flex items-center gap-1 px-3 py-1.5 rounded-full text-sm transition-all'
                  style={{
                    backgroundColor: activeFilter === key ? config.color : config.bgColor,
                    color: activeFilter === key ? 'white' : config.color,
                    border: `1px solid ${config.color}`
                  }}
                >
                  <span>{renderStatusIcon(key)}</span>
                  <span className='font-medium'>{config.label}</span>
                </button>
              ))}
              {activeFilter && (
                <button
                  onClick={() => handleFilterChange(null)}
                  className='flex items-center gap-1 px-3 py-1.5 border border-gray-300 rounded-full text-sm text-gray-700 hover:bg-gray-100'
                >
                  <span>Tutti</span>
                </button>
              )}
            </div>
          </div>
        )}

        {isLoading &&
          <div className='h-full w-full flex items-center justify-center'>
            <Loader1/>
          </div>
        }

        {!isLoading && filteredBookings.length > 0 ? (
          <>
            {activeFilter && (
              <div className='flex items-center justify-between bg-white p-3 rounded-lg mb-2'>
                <div className='flex items-center gap-2'>
                  <span className='text-sm text-gray-500'>Filtro attivo:</span>
                  <div
                    className='flex items-center gap-1 px-2 py-1 rounded-full text-sm'
                    style={{
                      backgroundColor: getStatusConfig(activeFilter).bgColor,
                      color: getStatusConfig(activeFilter).color
                    }}
                  >
                    <span>{renderStatusIcon(activeFilter)}</span>
                    <span className='font-medium'>{getStatusConfig(activeFilter).label}</span>
                  </div>
                </div>
                <button
                  onClick={() => handleFilterChange(null)}
                  className='text-sm text-gray-500 hover:text-[#133157]'
                >
                  Rimuovi filtro
                </button>
              </div>
            )}

            {filteredBookings.map((booking: any) => (
              <div
                key={booking.id}
                onClick={() => handleSelectBooking(booking)}
                className='bg-white rounded-lg shadow-sm border-l-4 p-4 cursor-pointer hover:shadow-md transition-shadow'
                style={{ borderLeftColor: getStatusConfig(booking.status).color }}
              >
                <div className='flex justify-between items-start mb-3'>
                  <div className='flex flex-col'>
                    <div className='flex items-center gap-2'>
                      <span className='text-sm text-gray-500'>Prenotazione ID</span>
                      {booking.is_manual ? (
                        <div className='flex items-center gap-1 px-2 py-0.5 bg-gray-100 rounded-full text-xs text-gray-600'>
                          <ClipboardList className='w-3 h-3' />
                          <span>Manuale</span>
                        </div>
                      ) : (
                        <div className='flex items-center gap-1 px-2 py-0.5 bg-blue-50 rounded-full text-xs text-blue-600'>
                          <Laptop className='w-3 h-3' />
                          <span>Channel</span>
                        </div>
                      )}
                    </div>
                    <span className='font-bold text-[#133157]'>#{booking.id.slice(0,8)}</span>
                  </div>
                  <div
                    className='flex items-center gap-1 px-3 py-1 rounded-full text-sm'
                    style={{
                      backgroundColor: getStatusConfig(booking.status).bgColor,
                      color: getStatusConfig(booking.status).color
                    }}
                  >
                    <span>{renderStatusIcon(booking.status)}</span>
                    <span className='font-medium'>{getStatusConfig(booking.status).label}</span>
                  </div>
                </div>

                <div className='grid grid-cols-2 gap-4'>
                  <div className='flex items-center gap-2'>
                    <Home className='w-4 h-4 text-[#133157]' />
                    <span className='text-sm truncate'>{booking.property.name}</span>
                  </div>
                  <div className='flex items-center gap-2'>
                    <User className='w-4 h-4 text-[#133157]' />
                    <span className='text-sm truncate'>{booking.customer.first_name} {booking.customer.last_name}</span>
                  </div>
                </div>

                <div className='mt-3 pt-3 border-t border-gray-100 flex justify-between items-center'>
                  <div className='flex items-center gap-2'>
                    <Calendar className='w-4 h-4 text-[#133157]' />
                    <div className='text-sm'>
                      <span className='text-gray-500'>{formatDate(booking.reservation_data.checkin_date)}</span>
                      <span className='mx-2'>→</span>
                      <span className='text-gray-500'>{formatDate(booking.reservation_data.checkout_date)}</span>
                    </div>
                  </div>
                  <span className='text-sm font-medium text-[#133157]'>
                    €{booking.reservation_data.total_price}
                  </span>
                </div>
              </div>
            ))}

            {/* Pagination Controls */}
            {filteredBookings.length > 0 && totalPages > 1 && (
              <div className='flex flex-col justify-center items-center mt-6 mb-2 gap-3'>
                <div className='flex items-center gap-2 bg-white rounded-lg shadow-sm p-2'>
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className={`p-2 rounded-md ${currentPage === 1 ? 'text-gray-400 cursor-not-allowed' : 'text-[#133157] hover:bg-gray-100'}`}
                    aria-label="Pagina precedente"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="15 18 9 12 15 6"></polyline>
                    </svg>
                  </button>

                  <div className='flex items-center'>
                    <span className='text-sm text-gray-700'>
                      Pagina <span className='font-medium'>{currentPage}</span> di <span className='font-medium'>{totalPages}</span>
                    </span>
                  </div>

                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className={`p-2 rounded-md ${currentPage === totalPages ? 'text-gray-400 cursor-not-allowed' : 'text-[#133157] hover:bg-gray-100'}`}
                    aria-label="Pagina successiva"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <polyline points="9 18 15 12 9 6"></polyline>
                    </svg>
                  </button>
                </div>

                {/* Page Size Selector */}
                <div className='flex items-center gap-2 text-sm text-gray-700'>
                  <span>Mostra:</span>
                  <select
                    value={pageSize}
                    onChange={(e) => {
                      setPageSize(Number(e.target.value))
                      setCurrentPage(1) // Reset to first page when changing page size
                    }}
                    className='p-1 border border-gray-300 rounded-md bg-white text-[#133157]'
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                  <span>prenotazioni per pagina</span>
                </div>
              </div>
            )}
          </>
        ) : !isLoading && (
          <div className='flex flex-col items-center justify-center p-8 text-center text-gray-500'>
            <Calendar className='w-12 h-12 mb-4 text-gray-400' />
            <p className='text-lg font-medium'>
              {activeFilter
                ? `Nessuna prenotazione con stato "${getStatusConfig(activeFilter).label}"`
                : 'Nessuna prenotazione presente'}
            </p>
            <p className='text-sm'>
              {activeFilter
                ? 'Prova a selezionare un altro filtro'
                : 'Le tue prenotazioni appariranno qui'}
            </p>
            {activeFilter && (
              <button
                onClick={() => handleFilterChange(null)}
                className='mt-4 px-4 py-2 bg-gray-100 text-[#133157] rounded-lg font-medium hover:bg-gray-200'
              >
                Mostra tutte le prenotazioni
              </button>
            )}
          </div>
        )}
      </div>
      <Navbar />
    </MobilePageStart>
  )
}

export default Page