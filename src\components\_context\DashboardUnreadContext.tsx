"use client"
import React, { createContext, useState, useEffect } from 'react';
import { getDashboardUnreadCounts } from '@/services/api';

interface DashboardUnreadCounts {
    unread_reviews_count: number;
    unread_notifications_count: number;
    unread_support_messages_count: number;
}

interface DashboardUnreadContextType {
    unreadCounts: DashboardUnreadCounts;
    isLoading: boolean;
    refreshUnreadCounts: () => Promise<void>;
    updateUnreadCount: (type: keyof DashboardUnreadCounts, count: number) => void;
}

const defaultUnreadCounts: DashboardUnreadCounts = {
    unread_reviews_count: 0,
    unread_notifications_count: 0,
    unread_support_messages_count: 0
};

export const DashboardUnreadContext = createContext<DashboardUnreadContextType>({
    unreadCounts: defaultUnreadCounts,
    isLoading: false,
    refreshUnreadCounts: async () => {},
    updateUnreadCount: () => {},
});

export const DashboardUnreadProvider = ({ children }: { children: React.ReactNode }) => {
    const [unreadCounts, setUnreadCounts] = useState<DashboardUnreadCounts>(defaultUnreadCounts);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Function to fetch unread counts from the API
    const refreshUnreadCounts = async () => {
        setIsLoading(true);
        try {
            const response = await getDashboardUnreadCounts();
            if (response.status === 200 && response.data) {
                setUnreadCounts({
                    unread_reviews_count: response.data.unread_reviews_count || 0,
                    unread_notifications_count: response.data.unread_notifications_count || 0,
                    unread_support_messages_count: response.data.unread_support_messages_count || 0
                });
            }
        } catch (error) {
            console.error('Error fetching dashboard unread counts:', error);
        } finally {
            setIsLoading(false);
        }
    };

    // Function to update a specific unread count locally
    const updateUnreadCount = (type: keyof DashboardUnreadCounts, count: number) => {
        setUnreadCounts(prev => ({
            ...prev,
            [type]: Math.max(0, count) // Ensure count is never negative
        }));
    };

    // Initial fetch of unread counts
    useEffect(() => {
        refreshUnreadCounts();

        // Set up periodic refresh every 30 seconds to keep counts updated
        const interval = setInterval(refreshUnreadCounts, 30000);

        return () => clearInterval(interval);
    }, []);

    return (
        <DashboardUnreadContext.Provider value={{
            unreadCounts,
            isLoading,
            refreshUnreadCounts,
            updateUnreadCount
        }}>
            {children}
        </DashboardUnreadContext.Provider>
    );
};
