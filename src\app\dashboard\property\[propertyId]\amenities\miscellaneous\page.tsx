'use client'

import HeaderPage from '@/components/_globals/headerPage'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import InputOnOff from '@/components/inputs/inputOnOff'
import Loader1 from '@/components/loaders/Loader1'
import { amenitiesCreateAndUpdate, amenitiesPropertyAmenities } from '@/services/api'
import { useRouter } from 'next/navigation'

import React, { useEffect, useState } from 'react'

interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({ params }: PageProps) {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [data, setData] = React.useState({
    videocall: false,
    bedlinen: false,
    electricity: false,
    fan: false,
    animals: false,
    privateentrance: false,
    airportshuttle: false,
    trainshuttle: false,
    iron: false,
    landline: false,
    bikedeposit: false,
    skideposit: false,
    minagecheckin: false,
    gardenfurniture: false,
    mealevents: false,
    adultbirthday: false,
    kidsbirthday: false,
    babyshower: false,
    weddings: false,
    parties: false,
    propertyissafe: false,
    firstaidkit: false,
    carbonmonoxidedetector: false,
    fireextinguisher: false,
    cctvpublic: false,
    cctvcommon: false,
    cctvprivate: false,
  })


  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      { name: 'Videocall', category: 'Miscellaneous', is_available: data.videocall ?? false },
      { name: 'Bedlinen', category: 'Miscellaneous', is_available: data.bedlinen ?? false },
      { name: 'Electricity', category: 'Miscellaneous', is_available: data.electricity ?? false },
      { name: 'Fan', category: 'Miscellaneous', is_available: data.fan ?? false },
      { name: 'Animals', category: 'Miscellaneous', is_available: data.animals ?? false },
      { name: 'Privateentrance', category: 'Miscellaneous', is_available: data.privateentrance ?? false },
      { name: 'Airportshuttle', category: 'Miscellaneous', is_available: data.airportshuttle ?? false },
      { name: 'Trainshuttle', category: 'Miscellaneous', is_available: data.trainshuttle ?? false },
      { name: 'Iron', category: 'Miscellaneous', is_available: data.iron ?? false },
      { name: 'Landline', category: 'Miscellaneous', is_available: data.landline ?? false },
      { name: 'Bikedeposit', category: 'Miscellaneous', is_available: data.bikedeposit ?? false },
      { name: 'Skideposit', category: 'Miscellaneous', is_available: data.skideposit ?? false },
      { name: 'Minagecheckin', category: 'Miscellaneous', is_available: data.minagecheckin ?? false },
      { name: 'Gardenfurniture', category: 'Miscellaneous', is_available: data.gardenfurniture ?? false },
      { name: 'Mealevents', category: 'Miscellaneous', is_available: data.mealevents ?? false },
      { name: 'Adultbirthday', category: 'Miscellaneous', is_available: data.adultbirthday ?? false },
      { name: 'Kidsbirthday', category: 'Miscellaneous', is_available: data.kidsbirthday ?? false },
      { name: 'Babyshower', category: 'Miscellaneous', is_available: data.babyshower ?? false },
      { name: 'Weddings', category: 'Miscellaneous', is_available: data.weddings ?? false },
      { name: 'Parties', category: 'Miscellaneous', is_available: data.parties ?? false },
      { name: 'Propertyissafe', category: 'Miscellaneous', is_available: data.propertyissafe ?? false },
      { name: 'Firstaidkit', category: 'Miscellaneous', is_available: data.firstaidkit ?? false },
      { name: 'Carbonmonoxidedetector', category: 'Miscellaneous', is_available: data.carbonmonoxidedetector ?? false },
      { name: 'Fireextinguisher', category: 'Miscellaneous', is_available: data.fireextinguisher ?? false },
      { name: 'Cctvpublic', category: 'Miscellaneous', is_available: data.cctvpublic ?? false },
      { name: 'Cctvcommon', category: 'Miscellaneous', is_available: data.cctvcommon ?? false },
      { name: 'Cctvprivate', category: 'Miscellaneous', is_available: data.cctvprivate ?? false },
    ];

    const call = await amenitiesCreateAndUpdate(params.propertyId, dataAmenities);

  };

  const handleFetchAmenities = async () => {
    if (!params.propertyId) return

     const call = await amenitiesPropertyAmenities(params.propertyId) as any

        if (call.status === 400){
          setIsLoading(false)
          return
        }
    
    
    
        
        // Update the data state based on the response
        const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) => index === 0 ? match.toLowerCase() : match.toUpperCase()) // Convert to camelCase
        .replace(/\s+/g, ''); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});


    setData(fetchedData);
    setIsLoading(false)
  }

  useEffect(() => {
    handleFetchAmenities()
  }, [])

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title='Miscellanea'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />



      {
        isLoading ?
          <div className='h-full w-full flex items-center justify-center'>
            <Loader1 />
          </div>
          :
          <div className='mt-4 px-4 h-full w-full flex flex-col gap-3'>
            <InputOnOff
              title='Wi-Fi supporta Videochiamate'
              value={data.videocall}
              onChange={(value) => {
                setData({
                  ...data,
                  videocall: value
                })
                handleUpdateAmenities({
                  ...data,
                  videocall: value
                })
              }}
            />

            <InputOnOff
              title='Lenzuola'
              value={data.bedlinen}
              onChange={(value) => {
                setData({
                  ...data,
                  bedlinen: value
                })
                handleUpdateAmenities({
                  ...data,
                  bedlinen: value
                })
              }}
            />

            <InputOnOff
              title='Elettricità'
              value={data.electricity}
              onChange={(value) => {
                setData({
                  ...data,
                  electricity: value
                })
                handleUpdateAmenities({
                  ...data,
                  electricity: value
                })
              }}
            />

            <InputOnOff
              title='Ventilatore'
              value={data.fan}
              onChange={(value) => {
                setData({
                  ...data,
                  fan: value
                })
                handleUpdateAmenities({
                  ...data,
                  fan: value
                })
              }}
            />

            <InputOnOff
              title='Animali ammessi'
              value={data.animals}
              onChange={(value) => {
                setData({
                  ...data,
                  animals: value
                })
                handleUpdateAmenities({
                  ...data,
                  animals: value
                })
              }}
            />

            <InputOnOff
              title='Entrata Privata'
              description='Gli ospiti possono entrare nella struttura senza passare attraverso spazi condivisi'
              value={data.privateentrance}
              onChange={(value) => {
                setData({
                  ...data,
                  privateentrance: value
                })
                handleUpdateAmenities({
                  ...data,
                  privateentrance: value
                })
              }}
            />

            <InputOnOff
              title='Trasporto Aeroporto'
              value={data.airportshuttle}
              onChange={(value) => {
                setData({
                  ...data,
                  airportshuttle: value
                })
                handleUpdateAmenities({
                  ...data,
                  airportshuttle: value
                })
              }}
            />

            <InputOnOff
              title='Trasporto Stazione'
              value={data.trainshuttle}
              onChange={(value) => {
                setData({
                  ...data,
                  trainshuttle: value
                })
                handleUpdateAmenities({
                  ...data,
                  trainshuttle: value
                })
              }}
            />

            <InputOnOff
              title='Ferro da stiro'
              value={data.iron}
              onChange={(value) => {
                setData({
                  ...data,
                  iron: value
                })
                handleUpdateAmenities({
                  ...data,
                  iron: value
                })
              }}
            />

            <InputOnOff
              title='Telefono fisso'
              value={data.landline}
              onChange={(value) => {
                setData({
                  ...data,
                  landline: value
                })
                handleUpdateAmenities({
                  ...data,
                  landline: value
                })
              }}
            />


            <InputOnOff
              title='Deposito Moto/Bici'
              value={data.bikedeposit}
              onChange={(value) => {
                setData({
                  ...data,
                  bikedeposit: value
                })
                handleUpdateAmenities({
                  ...data,
                  bikedeposit: value
                })
              }}
            />

            <InputOnOff
              title='Deposito Sci'
              value={data.skideposit}
              onChange={(value) => {
                setData({
                  ...data,
                  skideposit: value
                })
                handleUpdateAmenities({
                  ...data,
                  skideposit: value
                })
              }}
            />

            <InputOnOff
              title='Eta minima check-in 25 anni'
              value={data.minagecheckin}
              onChange={(value) => {
                setData({
                  ...data,
                  minagecheckin: value
                })
                handleUpdateAmenities({
                  ...data,
                  minagecheckin: value
                })
              }}
            />

            <InputOnOff
              title='Mobili Giardino'
              value={data.gardenfurniture}
              onChange={(value) => {
                setData({
                  ...data,
                  gardenfurniture: value
                })
                handleUpdateAmenities({
                  ...data,
                  gardenfurniture: value
                })
              }}
            />

            <InputOnOff
              title='Eventi Ristoro'
              description='Pranzo, Cena'
              value={data.mealevents}
              onChange={(value) => {
                setData({
                  ...data,
                  mealevents: value
                })
                handleUpdateAmenities({
                  ...data,
                  mealevents: value
                })
              }}
            />

            <InputOnOff
              title='Compleanno adulti'
              value={data.adultbirthday}
              onChange={(value) => {
                setData({
                  ...data,
                  adultbirthday: value
                })
                handleUpdateAmenities({
                  ...data,
                  adultbirthday: value
                })
              }}
            />

            <InputOnOff
              title='Compleanno bambini'
              value={data.kidsbirthday}
              onChange={(value) => {
                setData({
                  ...data,
                  kidsbirthday: value
                })
                handleUpdateAmenities({
                  ...data,
                  kidsbirthday: value
                })
              }}
            />


            <InputOnOff
              title='Babyshowers'
              value={data.babyshower}
              onChange={(value) => {
                setData({
                  ...data,
                  babyshower: value
                })
                handleUpdateAmenities({
                  ...data,
                  babyshower: value
                })
              }}
            />

            <InputOnOff
              title='Matrimoni'
              value={data.weddings}
              onChange={(value) => {
                setData({
                  ...data,
                  weddings: value
                })
                handleUpdateAmenities({
                  ...data,
                  weddings: value
                })
              }}
            />

            <InputOnOff
              title='Feste'
              value={data.parties}
              onChange={(value) => {
                setData({
                  ...data,
                  parties: value
                })
                handleUpdateAmenities({
                  ...data,
                  parties: value
                })
              }}
            />



            <InputOnOff
              title='La proprietà è sicura?'
              value={data.propertyissafe}
              onChange={(value) => {
                setData({
                  ...data,
                  propertyissafe: value
                })
                handleUpdateAmenities({
                  ...data,
                  propertyissafe: value
                })
              }}
            />

            <InputOnOff
              title='Kit Primo Soccorso'
              value={data.firstaidkit}
              onChange={(value) => {
                setData({
                  ...data,
                  firstaidkit: value
                })
                handleUpdateAmenities({
                  ...data,
                  firstaidkit: value
                })
              }}
            />

            <InputOnOff
              title='Rilevatore di monossido di carbonio'
              value={data.carbonmonoxidedetector}
              onChange={(value) => {
                setData({
                  ...data,
                  carbonmonoxidedetector: value
                })
                handleUpdateAmenities({
                  ...data,
                  carbonmonoxidedetector: value
                })
              }}
            />

            <InputOnOff
              title='Estintore'
              value={data.fireextinguisher}
              onChange={(value) => {
                setData({
                  ...data,
                  fireextinguisher: value
                })
                handleUpdateAmenities({
                  ...data,
                  fireextinghuisher: value
                })
              }}
            />

            <InputOnOff
              title='Telecamere aree pubbliche'
              value={data.cctvpublic}
              onChange={(value) => {
                setData({
                  ...data,
                  cctvpublic: value
                })
                handleUpdateAmenities({
                  ...data,
                  cctvpublic: value
                })
              }}
            />

            <InputOnOff
              title='Telecamere aree comuni'
              value={data.cctvcommon}
              onChange={(value) => {
                setData({
                  ...data,
                  cctvcommon: value
                })
                handleUpdateAmenities({
                  ...data,
                  cctvcommon: value
                })
              }}
            />

            <InputOnOff
              title='Telecamere aree private'
              value={data.cctvprivate}
              onChange={(value) => {
                setData({
                  ...data,
                  cctvprivate: value
                })
                handleUpdateAmenities({
                  ...data,
                  cctvprivate: value
                })
              }}
            />
          </div>
      }
    </MobilePageStart>
  )
}

export default Page



