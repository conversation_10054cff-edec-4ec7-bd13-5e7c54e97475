'use client'
import React, { useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import InputCounter from '@/components/inputs/inputCounter'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import InputSelectLabel from '@/components/inputs/inputSelectLabel'
import { createPropertyRatePlan, updatePropertyRatePlan, viewAllRatePlans } from '@/services/api'
import { mealPlanOptions } from '@/models/modelMealPlans'
import toast from 'react-hot-toast'
import { cancelationPolicyOptions } from '@/models/modelCancelationPolicy'
import CardAlert from '@/components/cards/CardAlert'
import Loader1 from '@/components/loaders/Loader1'


interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({ params }: PageProps) {
  const router = useRouter()
  const [isNew, setIsNew] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [propertyRates, setPropRates] = React.useState<any>({
    id: '',
    name: '',
    close_out_days: 0,
    close_out_time: '21:00',
    checkin_time: '10:00',
    description: '',
    meal_plan: 1
  })

  const handleFetchPropertyRates = async () => {
    setIsLoading(true)
    try {
      const call = await viewAllRatePlans(params.propertyId)

      if (call.length === 0) {
        setIsNew(true)
      } else {
        setPropRates({
          id: call[0].id,
          name: call[0].name,
          close_out_days: call[0].close_out_days,
          close_out_time: call[0].close_out_time,
          checkin_time: call[0].checkin_time,
          meal_plan: call[0].meal_plan,
          description: call[0].description,
        })
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleCreatePropertyRate = async () => {
    setIsLoading(true)
    try {
      const call = await createPropertyRatePlan(
        params.propertyId,
        propertyRates.name,
        propertyRates.close_out_days,
        propertyRates.close_out_time,
        propertyRates.checkin_time,
        propertyRates.description,
        propertyRates.meal_plan
      )

      if (call.status !== 201) {
        toast.error('Errore nel creare i property rates')
      } else {
        toast.success('Property Rates creati con successo')
        router.back()
      }
      return call.data.id
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdatePropertyRate = async () => {
    setIsLoading(true)
    try {
      const call = await updatePropertyRatePlan(
        params.propertyId,
        propertyRates.name,
        propertyRates.close_out_days,
        propertyRates.close_out_time,
        propertyRates.checkin_time,
        propertyRates.description,
        propertyRates.meal_plan,
        propertyRates.id
      )

      if (call !== 200) {
        toast.error('Errore nel creare i property rates')
      } else {
        toast.success('Tariffe immobiliari aggiornate con successo')
        router.back()
      }
    } finally {
      setIsLoading(false)
    }
  }

  const validateForm = () => {
    if (!propertyRates.name?.trim()) {
      toast.error('Il nome è obbligatorio')
      return false
    }
    if (!propertyRates.description?.trim()) {
      toast.error('La descrizione è obbligatoria')
      return false
    }
    if (!propertyRates.meal_plan) {
      toast.error('Seleziona una pensione')
      return false
    }
    if (isNew && !propertyRates.cancelation_policy) {
      toast.error('Seleziona una policy di cancellazione')
      return false
    }
    if (propertyRates.close_out_days < 0) {
      toast.error('I giorni di chiusura non possono essere negativi')
      return false
    }
    if (!propertyRates.checkin_time) {
      toast.error('Seleziona un orario di check-in')
      return false
    }
    if (!propertyRates.close_out_time) {
      toast.error('Seleziona un orario di check-out')
      return false
    }
    return true
  }

  const handleCreateAll = async () => {
    if (!validateForm()) return
    const step1 = await handleCreatePropertyRate()
  }
  
  const handleUpdateAll = async () => {
    if (!validateForm()) return
    const step1 = await handleUpdatePropertyRate()
  }

  useEffect(() => {
    handleFetchPropertyRates()
  }, [])

  if (isLoading) {
    return <Loader1 />
  }

  return (
    <MobilePageStart>
      <HeaderPage title='Piano tariffari'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>
        <div className='flex flex-col gap-4'>
          <InputLabelDescription
            label='Nome'
            isTextArea
            placeholder='Es. Tariffa Piena'
            value={propertyRates.name}
            onChange={(value) => {
              setPropRates({
                ...propertyRates,
                name: value
              })
            }}
          />
          <InputLabelDescription
            label='Descrizione'
            isTextArea
            placeholder='Es. Tariffa Piena'
            value={propertyRates.description}
            onChange={(value) => {
              setPropRates({
                ...propertyRates,
                description: value
              })
            }}
          />

          <InputSelectLabel
            label='Pensione'
            value={mealPlanOptions.find((value: any) => value.value == parseInt(propertyRates.meal_plan))?.label ?? 'Seleziona'
            }
            placeholder='Seleziona'
            onSelect={(value) => {
              setPropRates({
                ...propertyRates,
                meal_plan: value
              })
            }}
            options={mealPlanOptions}
          />
          {
            isNew ?
            <InputSelectLabel
              label='Policy Cancellazione'
              value={cancelationPolicyOptions.find((value: any) => value.value == propertyRates.cancelation_policy)?.label ?? 'Seleziona'
              }
              placeholder='Seleziona'
              onSelect={(value) => {
                setPropRates({
                  ...propertyRates,
                  cancelation_policy: value
                })
              }}
              options={cancelationPolicyOptions}
            /> : 
            <CardAlert
              title='Attenzione'
              message='Per modificare la Policy Cancellazione prego contattare il '
              learnMoreLink='/dashboard/chat'
              learnMoreText='Supporto Clienti'
              color='orange'
            />
          }
          <InputCounter
            label='Giorni di chiusura'
            value={propertyRates.close_out_days}
            onChange={(value) => {
              setPropRates({
                ...propertyRates,
                close_out_days: value
              })
            }}
          />
          <InputSelectLabel
            label='Orario di check-in'
            value={propertyRates.checkin_time}
            placeholder='Seleziona un orario'
            onSelect={(value) => {
                setPropRates({
                ...propertyRates,
                checkin_time: value
                })
              }}
              options={[
                { value: '10:00', label: '10:00' },
                { value: '11:00', label: '11:00' },
                { value: '12:00', label: '12:00' },
                { value: '13:00', label: '13:00' },
                { value: '14:00', label: '14:00' },
                { value: '15:00', label: '15:00' },
                { value: '16:00', label: '16:00' },
                { value: '17:00', label: '17:00' },
                { value: '18:00', label: '18:00' },
                { value: '19:00', label: '19:00' },
                { value: '20:00', label: '20:00' },
                { value: '21:00', label: '21:00' },
                { value: '22:00', label: '22:00' },
                { value: '23:00', label: '23:00' },
              ]}
              />
              <InputSelectLabel
              label='Orario di check-out'
              value={propertyRates.close_out_time}
              placeholder='Seleziona un orario'
              onSelect={(value) => {
                setPropRates({
                ...propertyRates,
                close_out_time: value
                })
              }}
              options={[
                { value: '07:00', label: '07:00' },
                { value: '08:00', label: '08:00' },
                { value: '09:00', label: '09:00' },
                { value: '10:00', label: '10:00' },
                { value: '11:00', label: '11:00' },
                { value: '12:00', label: '12:00' },
                { value: '13:00', label: '13:00' },
                { value: '14:00', label: '14:00' },
                { value: '15:00', label: '15:00' },
                { value: '16:00', label: '16:00' },
                { value: '17:00', label: '17:00' },
                { value: '18:00', label: '18:00' },
                { value: '19:00', label: '19:00' },
                { value: '20:00', label: '20:00' },
                { value: '21:00', label: '21:00' },
                { value: '22:00', label: '22:00' },
                { value: '23:00', label: '23:00' },
              ]}
              />

              <Button
              color='white'
              backgroundColor='var(--blue)'
              text='Continua'
              fontSize='14px'
              onClick={() => {
              isNew ? handleCreateAll() : handleUpdateAll();
            }}
          />
        </div>
      </div>
        <br />


    </MobilePageStart>
  )
}

export default Page
