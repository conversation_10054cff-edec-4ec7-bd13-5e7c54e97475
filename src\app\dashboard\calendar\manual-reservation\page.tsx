'use client'
import React, { Suspense, useEffect, useState } from 'react'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import InputCounter from '@/components/inputs/inputCounter'
import { useRouter, useSearchParams } from 'next/navigation'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import InputCalendar from '@/components/inputs/InputCalendar'
import CardAlert from '@/components/cards/CardAlert'
import { createManualBooking } from '@/services/api'
import toast from 'react-hot-toast'
import LoaderWhite from '@/components/loaders/LoaderWhite'
import { useCalendar } from "@/components/_context/CalendarContext";

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Page />
    </Suspense>
  )
}

function Page() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { clearCache } = useCalendar();

  // Query parameters
  const startDate = searchParams.get('start_date')
  const endDate = searchParams.get('end_date')
  const property = searchParams.get('property')

  // Form States
  const [selectedStartDate, setSelectedStartDate] = useState(startDate ?? '')
  const [selectedEndDate, setSelectedEndDate] = useState(endDate ?? '')
  const [name, setName] = useState('')
  const [surname, setSurname] = useState('')
  const [email, setEmail] = useState('')
  const [phone, setPhone] = useState('')
  const [address, setAddress] = useState('')
  const [city, setCity] = useState('')
  const [country, setCountry] = useState('')
  const [adults, setAdults] = useState(1)
  const [children, setChildren] = useState(0)
  const [depositTotal, setDepositTotal] = useState(0)
  const [bookingTotal, setBookingTotal] = useState(0)
  const [comments, setComments] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Error States
  const [errors, setErrors] = useState({
    startDate: '',
    endDate: '',
    name: '',
    surname: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    country: '',
    depositTotal: '',
    bookingTotal: ''
  })

  // Clear specific error
  const clearError = (field: keyof typeof errors) => {
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' })
    }
  }

  // Validate email format
  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Validate phone number format (basic validation)
  const isValidPhone = (phone: string): boolean => {
    // Allow different international formats
    const phoneRegex = /^[+]?[\d\s()-]{8,20}$/
    return phoneRegex.test(phone)
  }

  // Date validation helpers
  const formatDateString = (dateString: string): string => {
    // Format date to yyyy-mm-dd if it's not already
    if (!dateString) return ''
    try {
      const date = new Date(dateString)
      return date.toISOString().split('T')[0]
    } catch (e) {
      return dateString
    }
  }

  const isDateValid = (dateString: string): boolean => {
    if (!dateString) return false
    const date = new Date(dateString)
    return !isNaN(date.getTime())
  }

  const isEndDateAfterStartDate = (startDate: string, endDate: string): boolean => {
    if (!startDate || !endDate) return false
    const start = new Date(startDate)
    const end = new Date(endDate)
    return end > start
  }

  const validateForm = (): boolean => {
    const newErrors = { ...errors }
    let isValid = true

    // Property validation
    if (!property) {
      toast.error('Proprietà non selezionata')
      isValid = false
      // No field to associate this error with
    }

    // Date validations
    if (!selectedStartDate) {
      newErrors.startDate = 'La data di check-in è obbligatoria'
      isValid = false
    } else if (!isDateValid(selectedStartDate)) {
      newErrors.startDate = 'Data di check-in non valida'
      isValid = false
    } else {
      newErrors.startDate = ''
    }

    if (!selectedEndDate) {
      newErrors.endDate = 'La data di check-out è obbligatoria'
      isValid = false
    } else if (!isDateValid(selectedEndDate)) {
      newErrors.endDate = 'Data di check-out non valida'
      isValid = false
    } else {
      newErrors.endDate = ''
    }

    if (selectedStartDate && selectedEndDate && !isEndDateAfterStartDate(selectedStartDate, selectedEndDate)) {
      newErrors.endDate = 'La data di check-out deve essere successiva alla data di check-in'
      isValid = false
    }

    // Customer info validations
    if (!name.trim()) {
      newErrors.name = 'Il nome è obbligatorio'
      isValid = false
    } else {
      newErrors.name = ''
    }

    if (!surname.trim()) {
      newErrors.surname = 'Il cognome è obbligatorio'
      isValid = false
    } else {
      newErrors.surname = ''
    }

    if (!email.trim()) {
      newErrors.email = 'L\'email è obbligatoria'
      isValid = false
    } else if (!isValidEmail(email.trim())) {
      newErrors.email = 'Formato email non valido'
      isValid = false
    } else {
      newErrors.email = ''
    }

    if (!phone.trim()) {
      newErrors.phone = 'Il telefono è obbligatorio'
      isValid = false
    } else if (!isValidPhone(phone.trim())) {
      newErrors.phone = 'Formato telefono non valido'
      isValid = false
    } else {
      newErrors.phone = ''
    }

    // Payment validations
    if (depositTotal < 0) {
      newErrors.depositTotal = 'Il deposito non può essere negativo'
      isValid = false
    } else {
      newErrors.depositTotal = ''
    }

    if (bookingTotal <= 0) {
      newErrors.bookingTotal = 'Il totale della prenotazione deve essere maggiore di zero'
      isValid = false
    } else {
      newErrors.bookingTotal = ''
    }

    if (depositTotal > bookingTotal) {
      newErrors.depositTotal = 'Il deposito non può essere maggiore del totale'
      isValid = false
    }

    setErrors(newErrors)
    return isValid
  }

  const handleCreateManualBooking = async () => {
    if (!validateForm()) {
      return
    }

    try {
      setIsSubmitting(true)

      const customer = {
        first_name: name.trim(),
        last_name: surname.trim(),
        email: email.trim(),
        phone: phone.trim(),
        telephone: phone.trim(),
        address: address.trim(),
        // city: city.trim(),
        // state: city.trim(), 
        country: country.trim(),
      }

      const reservationData = {
        checkin_date: formatDateString(selectedStartDate),
        checkout_date: formatDateString(selectedEndDate),
        total_price: Number(bookingTotal),
        deposit: Number(depositTotal),
        number_of_adults: adults,
        number_of_children: children,
        number_of_guests: adults + children,
        number_of_infants: 0,
        remarks: comments.trim()
      }

      const response = await createManualBooking(property!, customer, reservationData)

      if (!response.error) {
        clearCache(); // Clear the calendar cache
        toast.success('Prenotazione creata con successo')
        setTimeout(() => {
          router.push("/dashboard/calendar")
        }, 1000)
      } else {
        // Handle specific error cases
        if (response.message?.includes('dates')) {
          toast.error('Errore nel riservare queste date, sono già occupate')
        } else {
          toast.error(response.message || 'Errore nella creazione della prenotazione')
        }
      }
    } catch (error) {
      console.error('Error creating manual booking:', error)
      toast.error('Si è verificato un errore. Riprova più tardi.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format currency inputs
  const formatCurrency = (value: string): string => {
    // Remove non-numeric characters except decimal point
    const numericValue = value.replace(/[^\d.]/g, '')
    
    // Ensure there's only one decimal point
    const parts = numericValue.split('.')
    if (parts.length > 2) {
      return parts[0] + '.' + parts.slice(1).join('')
    }
    
    return numericValue
  }

  useEffect(() => {
    // Validate if property exists on mount
    if (!property) {
      toast.error('Proprietà non specificata')
    }
    
    // Set min date for check-in calendar
    const today = new Date()
  }, [property])

  return (
    <MobilePageStart>
      <HeaderPage
        title='Riservazione Manuale'
        actionLeftIcon={
          () => {
            router.back()
          }
        }
        actionRightIcon={
          () => {
            router.back()
          }
        }
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col justify-between'>
        <div className='flex flex-col gap-4'>
          <InputCalendar
            label='Check-in'
            value={selectedStartDate}
            onChange={(value) => {
              setSelectedStartDate(value)
              clearError('startDate')
              
              // If end date is before new start date, update it
              if (selectedEndDate && !isEndDateAfterStartDate(value, selectedEndDate)) {
                // Set end date to day after new start date
                const startDate = new Date(value)
                startDate.setDate(startDate.getDate() + 1)
                setSelectedEndDate(startDate.toISOString().split('T')[0])
                clearError('endDate')
              }
            }}
            min={new Date().toISOString().split('T')[0]} // Don't allow dates in the past
            error={errors.startDate}
          />
          
          <InputCalendar
            label='Check-out'
            value={selectedEndDate}
            onChange={(value) => {
              setSelectedEndDate(value)
              clearError('endDate')
            }}
            min={selectedStartDate ? 
              (() => {
                const nextDay = new Date(selectedStartDate)
                nextDay.setDate(nextDay.getDate() + 1)
                return nextDay.toISOString().split('T')[0]
              })() : 
              new Date().toISOString().split('T')[0]
            }
            error={errors.endDate}
          />

          <CardAlert
            title='Attenzione'
            message='Queste informazioni sono solo per referenza personale'
            color='orange'
            style={{
              marginTop: '20px'
            }}
          />

          <InputLabelDescription
            label='Nome'
            value={name}
            onChange={(value) => {
              setName(value)
              clearError('name')
            }}
            error={errors.name}
          />
          
          <InputLabelDescription
            label='Cognome'
            value={surname}
            onChange={(value) => {
              setSurname(value)
              clearError('surname')
            }}
            error={errors.surname}
          />
          
          <InputLabelDescription
            label='Indirizzo email'
            value={email}
            onChange={(value) => {
              setEmail(value)
              clearError('email')
            }}
            error={errors.email}
          />
          
          <InputLabelDescription
            label='Telefono'
            value={phone}
            onChange={(value) => {
              setPhone(value)
              clearError('phone')
            }}
            error={errors.phone}
          />
          
          <InputLabelDescription
            label='Indirizzo'
            value={address}
            onChange={(value) => {
              setAddress(value)
              clearError('address')
            }}
            error={errors.address}
          />
          
          {/* <InputLabelDescription
            label='Città'
            value={city}
            onChange={(value) => {
              setCity(value)
              clearError('city')
            }}
            error={errors.city}
          />
           */}
          <InputLabelDescription
            label='Paese'
            value={country}
            onChange={(value) => {
              setCountry(value)
              clearError('country')
            }}
            error={errors.country}
          />

          <InputCounter
            value={adults}
            onChange={(value) => {
              // Ensure at least 1 adult
              setAdults(value < 1 ? 1 : value)
            }}
            label='Adulti'
            minimum={1} // At least one adult required
          />
          
          <InputCounter
            value={children}
            onChange={setChildren}
            label='Bambini'
            minimum={0}
          />

          <InputLabelDescription
            label='Totale Deposito'
            value={depositTotal.toString()}
            onChange={(value) => {
              const formatted = formatCurrency(value)
              setDepositTotal(formatted === '' ? 0 : parseFloat(formatted))
              clearError('depositTotal')
            }}
            error={errors.depositTotal}
          />

          <InputLabelDescription
            label='Importo totale'
            value={bookingTotal.toString()}
            onChange={(value) => {
              const formatted = formatCurrency(value)
              setBookingTotal(formatted === '' ? 0 : parseFloat(formatted))
              clearError('bookingTotal')
            }}
            error={errors.bookingTotal}
          />

          <InputLabelDescription
            label='Commenti'
            value={comments}
            onChange={setComments}
            isTextArea={true}
          />

        </div>
        <br />
        <Button
          color='white'
          backgroundColor='var(--blue)'
          text={isSubmitting ? 'Creazione in corso...' : 'Continua'}
          fontSize='14px'
          disabled={isSubmitting}
          onClick={handleCreateManualBooking}
        />
        <br />
      </div>
    </MobilePageStart>
  )
}