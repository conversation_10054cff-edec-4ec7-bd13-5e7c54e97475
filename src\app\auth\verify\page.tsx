'use client'

import Image from "next/image";
import styles from "../login/page.module.css";
import { Suspense, useEffect, useState } from "react";
import ButtonLoading from "@/components/buttons/ButtonLoading";
import Card from "@/components/cards/Card";
import Tag from "@/components/tags/Tag";
import TitleXL from "@/components/titles/TitleXL";

import ToastCustom from "@/components/toast/Toast";
import { Mail } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import InputCode from "@/components/inputs/inputCode";
import LoaderWhite from "@/components/loaders/LoaderWhite";
import MobilePage from "@/components/_globals/mobilePage";
import { verifyEmail } from "@/services/api";


export default function main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Verify />
    </Suspense>
  )
}


function Verify() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const email = searchParams.get('email')

  const [isVerifiable, setIsVerifiable] = useState(false) //this checks if the user actuallu needs to verify the email (if not send back to login)
  const [isVerifing, setIsVerifing] = useState(false) //this checks if the user is currently verifying the email (loading the verification call)

  const [code, setCode] = useState('')
  const [crono, setCrono] = useState(0)
  const maxTime = 120


  const handleVerifyEmail = async () => {
    if (isVerifing) return
    if (!email){
      ToastCustom('error', 'Email not found')
      setCode('')
      return
    }
    setIsVerifing(true)

    const call = await verifyEmail(email, code)


    if (call.status === 200) {
      ToastCustom('success', 'Email verificata con successo')
      setTimeout(() => {
        router.push('/auth/set-password?email=' + email)
      }, 3000)
    } else {
      ToastCustom('error', 'Codice di verifica non valido')
      setCode('')
    }

    setIsVerifing(false)
  }




  //Start counting seconds
  useEffect(() => {
    const interval = setInterval(() => {
      if (crono >= maxTime) {
        clearInterval(interval)
        ToastCustom('Any problem?', 'If you havent received the email, and checked your spam, click on the button below to send it again')
        return
      }
      setCrono(crono + 1)
    }, 1000);


    return () => clearInterval(interval);
  }, [crono]);

   useEffect(() => {
     if (code.length === 4) {
       handleVerifyEmail()
     }
   }, [code])
 




  return (
    <MobilePage>
      <h1 className={styles.title}>Inserisci il Codice</h1>

      <div className={styles.link4} style={{ textAlign: "center" }}>
        Inserisci il codice di 4 cifre inviato al tuo indirizzo email
      </div>

      {/* <div
        style={{
          fontSize: '1.125rem',
          color: 'var(--text-secondary)',
          maxWidth: '540px',
          textAlign: 'center'
        }}
      >
        {
          isVerifiable ||
            isVerifing
            ?
            <>
              Codice di verifica inviato a:
              <br />
              <span style={{ borderBottom: '.5px solid #ffffff50' }}>{email}</span>
            </>
            :
            <LoaderWhite />
        }
      </div> */}

      <div className={styles.containerInputs}>
        <InputCode
          value={code}
          onChange={(value: string) => {
            setCode(value.toString())
          }}
          onCompletion={() => {
            handleVerifyEmail()
          }}
          autoFocus={true}
        />
        <ButtonLoading
          deactive={crono < maxTime}
          text={
            <div className="flex flex-row gap-2 items-center">
              <Mail width={20} />
              <span className="font-semibold">Invia di nuovo</span>
            </div>
          }
          color="white"
          backgroundColor="var(--steam-color)"
          onClick={() => {
            //handleSendEmail()
          }}
        />
        <p
          style={{
            fontSize: '.825rem',
            color: 'var(--text-secondary)',
            maxWidth: '540px',
            margin: '0.5rem auto',
            textAlign: 'center',


          }}
        >
          {
            crono < maxTime &&
            `Puoi richiedere un codice nuovo tra ${maxTime - crono} secondi`
          }
        </p>

      </div>
    </MobilePage>
  );
}





