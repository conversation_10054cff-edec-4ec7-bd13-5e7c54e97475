'use client'
import React, { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { UserContext } from '@/components/_context/UserContext'
import { PropertyContext } from '@/components/_context/PropertyContext'
import Loader1 from '@/components/loaders/Loader1'
import toast from 'react-hot-toast'
import { CheckIcon, ChevronRightIcon, InformationCircleIcon } from '@heroicons/react/16/solid'
import Button from '@/components/buttons/Button'
import { getPropertyOnlineCheckInStatus } from '@/services/api'

function Page() {
  const router = useRouter()
  const { isNotOwnerPermission, isCustomer, hasBillingProfile } = useContext(UserContext)
  const { properties, isLoadingProperties, fetchProperties } = useContext(PropertyContext)
  const [isLoading, setIsLoading] = useState(true)
  const [propertyStatuses, setPropertyStatuses] = useState<Record<string, any>>({})
  const [showInfoBox, setShowInfoBox] = useState(false)

  // Check permissions
  useEffect(() => {
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile) {
      toast.error('Non hai i permessi necessari per accedere a questa sezione')
      router.push('/dashboard/settings/account')
    }
  }, [isNotOwnerPermission, isCustomer, hasBillingProfile, router])

  // Fetch properties only once on component mount
  useEffect(() => {
    // Skip if we don't have permissions
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile) {
      return
    }

    // Fetch properties if needed
    fetchProperties()
  }, [fetchProperties, isNotOwnerPermission, isCustomer, hasBillingProfile])

  // Fetch property statuses when properties are loaded
  useEffect(() => {
    // Skip if we don't have permissions or if properties are still loading
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile || isLoadingProperties) {
      return
    }

    // Skip if properties array is empty
    if (!properties || properties.length === 0) {
      setIsLoading(false)
      return
    }

    const loadPropertyStatuses = async () => {
      setIsLoading(true)
      try {
        const statuses: Record<string, any> = {}
        const onboardedActiveProperties = properties.filter(
          property => property.is_onboarded && property.is_active
        )

        // Use Promise.all to fetch all statuses in parallel
        await Promise.all(
          onboardedActiveProperties.map(async (property) => {
            try {
              const response = await getPropertyOnlineCheckInStatus(property.id)
              if (response.status === 200) {
                statuses[property.id] = response.data
              } else {
                statuses[property.id] = {
                  is_enabled: false,
                  istat_connected: false,
                  alloggati_connected: false
                }
              }
            } catch (error) {
              console.error(`Error fetching status for property ${property.id}:`, error)
              statuses[property.id] = {
                is_enabled: false,
                istat_connected: false,
                alloggati_connected: false
              }
            }
          })
        )

        setPropertyStatuses(statuses)
      } catch (error) {
        console.error('Error loading online check-in data:', error)
        toast.error('Errore durante il caricamento dei dati')
      } finally {
        setIsLoading(false)
      }
    }

    loadPropertyStatuses()
  }, [properties, isLoadingProperties, isNotOwnerPermission, isCustomer, hasBillingProfile])

  // Filter properties to only show onboarded ones
  const onboardedProperties = properties.filter(property =>
    property.is_onboarded && property.is_active
  )

  return (
    <MobilePageStart>
      <HeaderPage
        title='Check-in Online'
        actionLeftIcon={() => router.push('/dashboard/settings/account')}
        actionRightIcon={() => router.push('/dashboard/settings')}
      />

      {isLoading || isLoadingProperties ? (
        <div className="flex-1 flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>
          {/* Header Section */}
          <div>
            <h2 className='text-xl font-semibold mb-2'>Check-in online degli ospiti</h2>
            <p className='text-gray-600 text-sm'>
              Approfitta di tutti i vantaggi della funzione di check-in online gratuitamente:
            </p>
          </div>

          {/* Benefits List */}
          <div className='flex flex-col gap-3'>
            <div className='flex items-start gap-2'>
              <CheckIcon className='h-5 w-5 text-green-600 mt-0.5 flex-shrink-0' />
              <p className='text-sm'>Gli ospiti ricevono un modulo di check-in online prima dell'arrivo.</p>
            </div>
            <div className='flex items-start gap-2'>
              <CheckIcon className='h-5 w-5 text-green-600 mt-0.5 flex-shrink-0' />
              <p className='text-sm'>Le informazioni degli ospiti vengono inviate automaticamente alla polizia locale o all'autorità.</p>
            </div>
            <div className='flex items-start gap-2'>
              <CheckIcon className='h-5 w-5 text-green-600 mt-0.5 flex-shrink-0' />
              <p className='text-sm'>I documenti degli ospiti possono essere scansionati con un clic.</p>
            </div>
            <div className='flex items-start gap-2'>
              <CheckIcon className='h-5 w-5 text-green-600 mt-0.5 flex-shrink-0' />
              <p className='text-sm'>Scarica le informazioni degli ospiti in qualsiasi momento in caso di controllo.</p>
            </div>
          </div>

          {/* Info Box */}
          <div
            className='bg-blue-50 rounded-lg p-4 border border-blue-100'
            onClick={() => setShowInfoBox(!showInfoBox)}
          >
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <InformationCircleIcon className='h-5 w-5 text-blue-600' />
                <p className='text-sm font-medium text-blue-800'>
                  Non sei sicuro di come funziona o come connetterti a un'autorità (es. SES.HOSPEDAJES, SEF/SIBA, AVS)?
                </p>
              </div>
              <ChevronRightIcon
                className={`h-5 w-5 text-blue-600 transition-transform ${showInfoBox ? 'rotate-90' : ''}`}
              />
            </div>

            {showInfoBox && (
              <div className='mt-3 text-sm text-blue-800'>
                <p>
                  Il check-in online ti permette di automatizzare l'invio dei dati degli ospiti alle autorità competenti come ISTAT e Alloggati Web.
                  Configurando questa funzionalità, potrai:
                </p>
                <ul className='list-disc pl-5 mt-2 space-y-1'>
                  <li>Risparmiare tempo nell'inserimento manuale dei dati</li>
                  <li>Evitare errori di trascrizione</li>
                  <li>Essere sempre conforme alle normative locali</li>
                </ul>
                <Button
                  text="Scopri di più"
                  backgroundColor="#113158"
                  color="white"
                  fontSize="14px"
                  className="mt-3 w-full"
                  onClick={() => {
                    // This would link to a help page or documentation
                    router.push('/dashboard/settings/help')
                  }}
                />
              </div>
            )}
          </div>

          {/* Properties Section */}
          <div>
            <h3 className='text-lg font-medium mb-3'>Attiva check-in online</h3>

            {onboardedProperties.length === 0 ? (
              <div className='bg-gray-50 rounded-lg p-4 text-center'>
                <p className='text-gray-600 text-sm'>
                  Non hai proprietà attive disponibili per il check-in online.
                </p>
              </div>
            ) : (
              <div className='space-y-3'>
                {onboardedProperties.map(property => (
                  <div
                    key={property.id}
                    className='bg-white rounded-lg border border-gray-200 p-4 shadow-sm'
                  >
                    <div className='flex justify-between items-center'>
                      <div>
                        <h4 className='font-medium'>{property.name}</h4>
                        <div className='flex flex-col mt-2 space-y-1'>
                          <div className='flex items-center'>
                            <span className='text-sm text-gray-600 w-36'>Stato connessione:</span>
                            <span className={`text-sm ml-2 px-2 py-0.5 rounded-full ${
                              propertyStatuses[property.id]?.is_enabled
                                ? 'bg-green-100 text-green-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {propertyStatuses[property.id]?.is_enabled ? 'Attivo' : 'Inattivo'}
                            </span>
                          </div>
                          <div className='flex items-center'>
                            <span className='text-sm text-gray-600 w-36'>Connessione ISTAT:</span>
                            <span className={`text-sm ml-2 px-2 py-0.5 rounded-full ${
                              propertyStatuses[property.id]?.istat_connected
                                ? 'bg-green-100 text-green-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {propertyStatuses[property.id]?.istat_connected ? 'Connesso' : 'Non connesso'}
                            </span>
                          </div>
                          <div className='flex items-center'>
                            <span className='text-sm text-gray-600 w-36'>Connessione Alloggati:</span>
                            <span className={`text-sm ml-2 px-2 py-0.5 rounded-full ${
                              propertyStatuses[property.id]?.alloggati_connected
                                ? 'bg-green-100 text-green-700'
                                : 'bg-gray-100 text-gray-700'
                            }`}>
                              {propertyStatuses[property.id]?.alloggati_connected ? 'Connesso' : 'Non connesso'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        text="Configura"
                        backgroundColor="#113158"
                        color="white"
                        fontSize="14px"
                        onClick={() => {
                          router.push(`/dashboard/settings/account/online-check-in/configure/${property.id}`)
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </MobilePageStart>
  )
}

export default Page
