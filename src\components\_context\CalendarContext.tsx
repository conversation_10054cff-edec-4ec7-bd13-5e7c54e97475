'use client'
import React, { createContext, useContext, useState, ReactNode } from 'react';
import { viewPropertyBlockedDates, viewPropertyBookings, deactivateBookingBlocks } from '@/services/api';
import { useRouter } from 'next/navigation';
import toast from 'react-hot-toast';

// Define types for our context
interface CalendarContextType {
  // Cached data
  blockedDates: Record<string, any[]>;
  bookings: Record<string, any[]>;

  // Loading states
  isLoadingBlocks: boolean;
  isLoadingBookings: boolean;

  // Methods
  fetchBlockedDates: (propertyId: string, startDate: string, endDate: string) => Promise<any[]>;
  fetchBookings: (propertyId: string, startDate: string, endDate: string) => Promise<any[]>;
  deactivateBlock: (blockId: string) => Promise<boolean>;
  clearCache: () => void;
}

// Create the context with default values
const CalendarContext = createContext<CalendarContextType>({
  blockedDates: {},
  bookings: {},
  isLoadingBlocks: false,
  isLoadingBookings: false,
  fetchBlockedDates: async () => [],
  fetchBookings: async () => [],
  deactivateBlock: async () => false,
  clearCache: () => {},
});

// Custom hook to use the calendar context
export const useCalendar = () => useContext(CalendarContext);

// Provider component
interface CalendarProviderProps {
  children: ReactNode;
}

export const CalendarProvider = ({ children }: CalendarProviderProps) => {
  const router = useRouter();
  // State for cached data
  const [blockedDates, setBlockedDates] = useState<Record<string, any[]>>({});
  const [bookings, setBookings] = useState<Record<string, any[]>>({});

  // Loading states
  const [isLoadingBlocks, setIsLoadingBlocks] = useState(false);
  const [isLoadingBookings, setIsLoadingBookings] = useState(false);

  // Generate a cache key based on property ID and date range
  const generateCacheKey = (propertyId: string, startDate: string, endDate: string) => {
    return `${propertyId}_${startDate}_${endDate}`;
  };

  // Fetch blocked dates with caching
  const fetchBlockedDates = async (propertyId: string, startDate: string, endDate: string) => {
    const cacheKey = generateCacheKey(propertyId, startDate, endDate);

    // Return cached data if available
    if (blockedDates[cacheKey]) {
      return blockedDates[cacheKey];
    }

    // Otherwise fetch from API
    setIsLoadingBlocks(true);
    try {
      const data = await viewPropertyBlockedDates(propertyId, startDate, endDate);

      // Update cache
      setBlockedDates(prev => ({
        ...prev,
        [cacheKey]: data
      }));

      setIsLoadingBlocks(false);
      return data;
    } catch (error) {
      console.error('Error fetching blocked dates:', error);
      setIsLoadingBlocks(false);
      return [];
    }
  };

  // Fetch bookings with caching
  const fetchBookings = async (propertyId: string, startDate: string, endDate: string) => {
    const cacheKey = generateCacheKey(propertyId, startDate, endDate);

    // Return cached data if available
    if (bookings[cacheKey]) {
      return bookings[cacheKey];
    }

    // Otherwise fetch from API
    setIsLoadingBookings(true);
    try {
      const data = await viewPropertyBookings(propertyId, startDate, endDate);
      
      // Filter out cancelled reservations
      const filteredData = data.filter((booking: any) => booking.status !== 'cancelled');

      // Update cache
      setBookings(prev => ({
        ...prev,
        [cacheKey]: filteredData
      }));

      setIsLoadingBookings(false);
      return filteredData;
    } catch (error) {
      console.error('Error fetching bookings:', error);
      setIsLoadingBookings(false);
      return [];
    }
  };

  // Deactivate a booking block
  const deactivateBlock = async (blockId: string) => {
    try {
      await deactivateBookingBlocks(blockId);
      router.push("/dashboard"); 
      toast.success('Blocco prenotazione disattivato con successo'); 
      
      return true;
    } catch (error) {
      console.error('Error deactivating booking block:', error);
      toast.error('Errore durante la disattivazione del blocco');
      return false;
    }
  };

  // Clear all cached data
  const clearCache = () => {
    setBlockedDates({});
    setBookings({});
    // Reset loading states as well
    setIsLoadingBlocks(false);
    setIsLoadingBookings(false);
  };

  // Value object to be provided by the context
  const value = {
    blockedDates,
    bookings,
    isLoadingBlocks,
    isLoadingBookings,
    fetchBlockedDates,
    fetchBookings,
    deactivateBlock,
    clearCache
  };

  return (
    <CalendarContext.Provider value={value}>
      {children}
    </CalendarContext.Provider>
  );
};
