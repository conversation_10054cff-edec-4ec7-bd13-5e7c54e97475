'use client'

import React from 'react'
import style from './button.module.css'

interface ButtonProps extends React.PropsWithChildren  {
    /**
     * Function to call when the button is hovered
     */
    onHover?: () => void;

    /**
     * Function to call when the button is clicked
     */
    onClick: () => void;

    /**
     * Optional icon to display before the text
     */
    icon?: React.ReactNode;

    /**
     * Text color of the button
     */
    color: string;

    /**
     * Background color of the button
     */
    backgroundColor: string;

    /**
     * Text to display on the button
     */
    text: string;

    /**
     * Font size of the button text
     */
    fontSize?: string;

    /**
     * Whether the button is disabled
     */
    disabled?: boolean;

    /**
     * Additional CSS classes to apply to the button
     */
    className?: string;

    /**
     * Border style for the button
     */
    border?: string;
}

/**
 * A reusable button component with customizable styling
 */
function Button({
  onClick,
  onHover,
  icon,
  color,
  backgroundColor,
  text,
  fontSize,
  disabled = false,
  className = '',
  border
}: ButtonProps) {
  return (
    <div
      onMouseEnter={onHover}
      onMouseLeave={onHover}
      className={`${style.component} ${className}`}
      style={{
        background: backgroundColor,
        color: color,
        cursor: disabled ? 'not-allowed' : 'pointer',
        opacity: disabled ? 0.7 : 1,
        border: border,
        pointerEvents: disabled ? 'none' : 'auto'
      }}
      onClick={() => {
        if (!disabled) {
          onClick();
        }
      }}
    >
      {icon && (
        <span className={style.iconContainer}>
          {icon}
        </span>
      )}
      <div
        style={{
          fontSize: fontSize ?? '14px'
        }}
      >
        {text}
      </div>
    </div>
  )
}

export default Button