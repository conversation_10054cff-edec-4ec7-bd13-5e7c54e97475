'use client'

import React, { useEffect, useState } from 'react'
import style from './input.module.css'

function InputCalendar(
    props: {
        label: string,
        value: string,
        onChange: (e: any) => void,
        name?: string,
        type?: string
        required?: boolean
        placeholder?: string
        style?: React.CSSProperties,
        rules?: { type: string, value?: any, message?: string }[], //set the array of string errors
        error?: string
        max?: string,
        min?: string,
        readOnly?: boolean,
    }
) {
    return (
        <div className={style.component}>
            <p style={{ color: "black" }}>
                {props.label}
                {
                    props.required &&
                    <label>*</label>
                }
            </p>
            <div className={style.inputContainer} style={{padding: '0', marginTop: '10px'}}>
                <input
                    type='date'
                    style={{
                        color: 'black',
                        fontSize: '12px',
                        background: props.readOnly ? '#f5f5f5' : 'white',
                        width: '100%',
                        padding: '0 10px',
                        borderColor: props.error ? 'red' : undefined, // Add red border if there's an error
                        cursor: props.readOnly ? 'default' : 'pointer'
                    }}
                    readOnly={props.readOnly}
                    placeholder={props.placeholder}
                    value={props.value}
                    onChange={(e) => {
                        props.onChange(e.target.value)
                    }}
                />
            </div>
            {props.error && (
                <p style={{
                    color: 'red',
                    fontSize: '12px',
                    margin: '4px 0 0',
                    padding: 0
                }}>
                    {props.error}
                </p>
            )}
        </div>
    )
}

export default InputCalendar
