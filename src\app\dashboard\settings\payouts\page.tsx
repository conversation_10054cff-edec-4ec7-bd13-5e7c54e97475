'use client'
import React, { useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'

import Button from '@/components/buttons/Button'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import { useRouter } from 'next/navigation'
import { getPayouts } from '@/services/api'
import InputCalendar from '@/components/inputs/InputCalendar'
import toast from 'react-hot-toast'
import Card from '@/components/cards/Card'


function Page() {
  const router = useRouter()

  const [startDate, setStartDate] = useState<string | any>('2024-03-08')
  const [endDate, setEndDate] = useState<string | any>('2026-03-09')


  const [payouts, setPayouts] = useState<any>([])
  const [isLoading, setIsLoading] = useState(true)


  const handleGetPayouts = async () => {

    if (!startDate || !endDate) {
      return toast.error('Selezionare le date perfavore')
    }
    const call = await getPayouts(startDate, endDate)
    
    setPayouts(call.data)
    setIsLoading(false)
  }


  useEffect(() => {
    handleGetPayouts()
  }, [startDate, endDate])



  return (
    <MobilePageStart>
      <HeaderPage title='Pagamenti'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>

        <InputCalendar
          value={startDate}
          label='Inizio'
          onChange={setStartDate}
        />
        <InputCalendar
          value={endDate}
          label='Fine'
          onChange={setEndDate}
        />

        {
          !isLoading &&
          payouts.length > 0 &&
          payouts.map((payout: any) => {
            return (
              <Card
                key={payout.id}
                style={{ width: '100%', padding: '10px 20px', display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexDirection: 'row' }}
                onClick={() => {

                }}
              >

                <p>{payout.amount} {payout.currency}</p>
                <div className='bg-[var(--accent)] py-2 px-6  text-white text-[14px] rounded-lg'>{payout.status}</div>
              </Card>

            )
          })
        }
        {
          !isLoading &&
          payouts.length == 0 &&
          <div className='flex flex-col items-center h-[400px] justify-center'>
            <p className='text-[23px] text-[var(--blue)]'>Nessun Pagamento effettuato</p>
            <p className='text-[13px] text-[var(--blue)]'>Torna quando saranno stati fatti pagamenti</p>
          </div>
        }




      </div>


    </MobilePageStart>
  )
}

export default Page