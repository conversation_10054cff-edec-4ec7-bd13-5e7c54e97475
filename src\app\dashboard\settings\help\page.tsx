'use client'
import React from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import Navbar from '@/components/_globals/navbar'
import HeaderPage from '@/components/_globals/headerPage'
import { useRouter } from 'next/navigation'
import Title from '@/components/titles/Title'
import Card from '@/components/cards/Card'
import Link from 'next/link'


function Page() {
  const router = useRouter()

  return (
    <MobilePageStart>
      <HeaderPage
        title='Aiuto & Supporto'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push('/dashboard/settings')
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-4'>

        <Title
          title='Domande frequenti'
          subtitle='Se hai domande o dubbi, consulta la nostra sezione di guide'
        />

        <Card style={{ width: '100%', background: 'white', padding: '20px 20px', alignItems: 'flex-start', gap: '10px' }}>
          <div>
            <p className='text-[14px]'>Cerchi aiuto?</p>
            <p className='text-[10px]'>Trova aiuti e informazioni sulla gestione della tua proprietà su Heibooky</p>
          </div>
          <Link className='text-[10px] text-[var(--accent)]' style={{textDecoration: 'underline'}} href="/dashboard/chat">Vai al nostro centro assistenza </Link>
        </Card>

        <Card style={{ width: '100%', background: 'white', padding: '20px 20px', alignItems: 'flex-start', gap: '10px' }}>
          <div>
            <p className='text-[14px]'>Cerca Templates</p>
            <p className='text-[10px]'>Trova i nostri template per business cards, checklist, regole della casa</p>
          </div>
          <a className='text-[10px] text-[var(--accent)]' style={{textDecoration: 'underline'}} href="/dashboard/templates">Templates</a>
        </Card>

        <p className='text-center text-[11px] mt-4'>
          se non hai trovato quello che cercavi, perfavore <Link href={'/dashboard/chat'} className='text-[var(--accent)]'>contattaci</Link>
        </p>

     

      </div>
      <Navbar />
    </MobilePageStart>
  )
}

export default Page