'use client'
import React, { Suspense, useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import { useRouter, useSearchParams } from 'next/navigation'
import CardAlert from '@/components/cards/CardAlert'
import { getPropertyRates } from '@/services/api'
import toast from 'react-hot-toast'
import LoaderWhite from '@/components/loaders/LoaderWhite'
import { Calendar, Clock, PlusCircle } from 'lucide-react'

export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Page />
    </Suspense>
  )
}

function Page() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // States
  const startDate = searchParams.get('start_date')
  const endDate = searchParams.get('end_date')
  const property = searchParams.get('property')
  const [roomRates, setRoomRates] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  const handleFetchRoomRates = async () => {
    setIsLoading(true)
    if (!property) {
      console.error('Parametri query mancanti o incorretti')
      toast.error('Proprietà non specificata')
      setIsLoading(false)
      return
    }

    try {
      const call = await getPropertyRates(property)
      setRoomRates(call)
    } catch (error) {
      console.error('Errore nel recupero delle tariffe:', error)
      toast.error('Impossibile caricare le stagioni')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    handleFetchRoomRates()
  }, [property])

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  const getSeasonStatus = (startDate: string, endDate: string) => {
    const now = new Date()
    const start = new Date(startDate)
    const end = new Date(endDate)

    if (now >= start && now <= end) {
      return {
        status: 'active',
        label: 'In corso',
        color: '#4CAF50',
        bgColor: '#E8F5E9',
        icon: <div className="w-4 h-4 text-green-500">✓</div>
      }
    } else if (now < start) {
      return {
        status: 'upcoming',
        label: 'Prossima stagione',
        color: '#FF9800',
        bgColor: '#FFF3E0',
        icon: <Clock className="w-4 h-4 text-[#febd49]" />
      }
    } else {
      return {
        status: 'past',
        label: 'Terminata',
        color: '#9E9E9E',
        bgColor: '#F5F5F5',
        icon: <div className="w-4 h-4 text-gray-400">✓</div>
      }
    }
  }

  return (
    <MobilePageStart isNavbar>
      <HeaderPage
        title='Stagioni proprietà'
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push('/dashboard')}
      />

      {/* Main content with proper scrolling */}
      <div className='flex-1 w-full overflow-auto bg-gray-50 pb-24'>
        <div className='p-3 flex flex-col gap-3'>
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <LoaderWhite />
            </div>
          ) : roomRates.length > 0 ? (
            roomRates.map((rate: any) => {
              const seasonStatus = getSeasonStatus(rate.start_date, rate.end_date)
              
              return (
                <div 
                  key={rate.id} 
                  className='bg-white rounded-lg overflow-hidden shadow-sm cursor-pointer'
                  onClick={() => {/* Handle click here */}}
                >
                  <div className='bg-[#133157] text-white p-3'>
                    <div className='flex justify-between items-center'>
                      <div className='flex items-center gap-2'>
                        <Calendar className='w-5 h-5' />
                        <h3 className='font-bold'>Stagione {formatDate(rate.start_date).split('/')[2]}</h3>
                      </div>
                      <div 
                        className='px-3 py-1 rounded-full text-xs font-medium' 
                        style={{ backgroundColor: seasonStatus.bgColor, color: seasonStatus.color }}
                      >
                        {seasonStatus.label}
                      </div>
                    </div>
                  </div>
                  
                  <div className='p-4'>
                    <div className='grid grid-cols-2 gap-4 mb-3'>
                      <div className='border-r border-gray-200 pr-2'>
                        <div className='flex items-center gap-1 mb-1'>
                          <Calendar className='w-4 h-4 text-[#133157]' />
                          <span className='text-gray-500 text-xs'>Data inizio</span>
                        </div>
                        <p className='font-medium text-sm'>{formatDate(rate.start_date)}</p>
                      </div>
                      <div>
                        <div className='flex items-center gap-1 mb-1'>
                          <Calendar className='w-4 h-4 text-[#133157]' />
                          <span className='text-gray-500 text-xs'>Data fine</span>
                        </div>
                        <p className='font-medium text-sm'>{formatDate(rate.end_date)}</p>
                      </div>
                    </div>
                    
                    <div className='border-t border-gray-100 pt-3'>
                      <div className='flex justify-between items-center'>
                        <div className='flex flex-col'>
                          <div className='flex items-center gap-1'>
                            <span className='text-[#133157] text-sm'>€</span>
                            <span className='text-gray-500 text-xs'>Tariffa</span>
                          </div>
                          <p className='font-medium text-[#133157]'>€{rate.rate}</p>
                        </div>
                        
                        <div className='flex gap-3'>
                          <div className='flex flex-col items-center'>
                            <span className='text-xs text-gray-500'>Min</span>
                            <span className='font-medium bg-blue-50 text-[#133157] px-2 py-1 rounded text-sm'>{rate.minimum_stay}</span>
                          </div>
                          <div className='flex flex-col items-center'>
                            <span className='text-xs text-gray-500'>Max</span>
                            <span className='font-medium bg-blue-50 text-[#133157] px-2 py-1 rounded text-sm'>{rate.maximum_stay}</span>
                          </div>
                          {/* <ChevronRight className='w-5 h-5 text-gray-400 self-center' /> */}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })
          ) : (
            <div className="py-10">
              <CardAlert
                title='Nessuna stagione trovata'
                message='Non ci sono stagioni create per questa proprietà. Clicca sul bottone sottostante per crearne una nuova.'
                color='orange'
              />
            </div>
          )}
        </div>
      </div>

      {/* Fixed footer */}
      <div className='fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-100 shadow-lg z-10'>
        <Button
          color='white'
          backgroundColor='var(--blue)'
          text='Crea stagione'
          fontSize='14px'
          onClick={() => {
            router.push(`/dashboard/calendar/pricing/create-season?start_date=${startDate}&end_date=${endDate}&property=${property}`)
          }}
          icon={<PlusCircle className="w-4 h-4 mr-2" />}
        />
      </div>
    </MobilePageStart>
  )
}