'use client';

import { useContext } from 'react';
import { AuthContext } from '@/components/_context/AuthContext';
import Button from '@/components/buttons/Button';
import { LogOut } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface LogoutButtonProps {
  /**
   * The text to display on the button
   * @default "Logout"
   */
  text?: string;

  /**
   * Additional CSS classes to apply to the button
   */
  className?: string;

  /**
   * Text color of the button
   * @default "white"
   */
  color?: string;

  /**
   * Background color of the button
   * @default "#113158" (brand blue)
   */
  backgroundColor?: string;

  /**
   * Font size of the button text
   * @default "15px"
   */
  fontSize?: string;

  /**
   * Whether to show the logout icon
   * @default true
   */
  showIcon?: boolean;

  /**
   * Whether to use the full width of the container
   * @default true
   */
  fullWidth?: boolean;

  /**
   * Whether to position the button at the bottom of the container
   * @default false
   */
  stickyBottom?: boolean;

  /**
   * Custom styles to apply to the container
   */
  containerStyle?: React.CSSProperties;
}

/**
 * A standardized logout button component that uses the AuthContext to handle logout
 *
 * @example
 * // Basic usage
 * <LogoutButton />
 *
 * @example
 * // Custom styling
 * <LogoutButton
 *   text="Esci"
 *   backgroundColor="#F44336"
 *   showIcon={false}
 * />
 *
 * @example
 * // Sticky to bottom
 * <LogoutButton stickyBottom />
 */
const LogoutButton = ({
  text = 'Logout',
  className = '',
  color = 'white',
  backgroundColor = '#113158', // Brand blue
  fontSize = '15px',
  showIcon = true,
  fullWidth = true,
  stickyBottom = false,
  containerStyle = {}
}: LogoutButtonProps) => {
  const { logout, isLoading } = useContext(AuthContext);
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/auth/login');
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const buttonContent = (
    <Button
      text={isLoading ? 'Uscita...' : text}
      onClick={handleLogout}
      color={color}
      backgroundColor={backgroundColor}
      fontSize={fontSize}
      disabled={isLoading}
      icon={showIcon ? <LogOut size={18} /> : undefined}
      className={`transition-all duration-200 hover:opacity-90 ${className}`}
    />
  );

  if (stickyBottom) {
    return (
      <div
        style={{
          position: 'sticky',
          bottom: '20px',
          width: fullWidth ? '100%' : 'auto',
          padding: '0 16px',
          marginTop: 'auto',
          zIndex: 10,
          ...containerStyle
        }}
      >
        {buttonContent}
      </div>
    );
  }

  return (
    <div
      style={{
        width: fullWidth ? '100%' : 'auto',
        ...containerStyle
      }}
    >
      {buttonContent}
    </div>
  );
};

export default LogoutButton;
