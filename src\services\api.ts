// Token management is now handled by tokenService.ts
import {
  getAccessToken,
  setAccessToken,
  getRefreshToken,
  setRefreshToken,
  clearTokens,
} from './tokenService';

// Import the apiClient for making API requests
import { apiClient } from './apiClient';

// For backward compatibility
export const setToken = (token: string, refreshToken?: string) => {
  setAccessToken(token);
  if (refreshToken) {
    setRefreshToken(refreshToken);
  }
}
export const getToken = () => {
  return getAccessToken();
}
export const deleteToken = () => {
  clearTokens();
}

//Token API
export const verifyToken = async () => {
  const token = getToken();

  if (!token) {
    return {
      status: 401
    };
  }

  try {
    const response = await apiClient.post('/users/verify-token/', { token });

    return {
      status: response.status
    };
  } catch (error) {
    console.error('Token verification error:', error);
    return {
      status: 500
    };
  }
}
export const refreshToken = async (token: string) => {
  try {
    const response = await apiClient.post('/users/refresh-token/', { refresh: token });
    const data = await response.json();

    // If the response contains new tokens, update them
    if (data.access && data.refresh) {
      setAccessToken(data.access);
      setRefreshToken(data.refresh);
    }

    return data;
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
}

//Account (Auth)
export const login = async (email: string, password: string) => {
  try {
    const response = await apiClient.post('/users/login/', { email, password });
    const data = await response.json();

    // If login is successful and we have tokens, store them
    if (response.status === 200 && data.access) {
      setAccessToken(data.access);
      if (data.refresh) {
        setRefreshToken(data.refresh);
      }
    }

    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Login error:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
};
export const logout = async () => {
  const refreshTokenValue = getRefreshToken();

  try {
    const response = await apiClient.post('/users/logout/', {
      refresh: refreshTokenValue || getToken()
    });

    // Clear tokens regardless of the response
    clearTokens();

    const data = await response.json();
    return {
      data,
      status: response.status
    };
  } catch (error) {
    // Clear tokens even if the API call fails
    clearTokens();
    console.error('Logout error:', error);
    return {
      data: { message: 'Logged out' },
      status: 200
    };
  }
}
export const register = async (name: string, email: string, phone: string) => {
  try {
    const response = await apiClient.post('/users/signup/', { email, name, phone });
    return await response.json();
  } catch (error) {
    console.error('Registration error:', error);
    return { error: 'Network error occurred' };
  }
}

export const verifyEmail = async (email: string, code: string) => {

  try {
    const response = await apiClient.post('/users/verify-email/', {
      email,
      verification_code: code
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Email verification error:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}
export const setPassword = async (email: string, password: string, password_confirm: string) => {
  try {
    const response = await apiClient.post('/users/set-password/', {
      email,
      password,
      password_confirm
    });

    return await response.json();
  } catch (error) {
    console.error('Set password error:', error);
    return { error: 'Network error occurred' };
  }
}

export const resetPassword = async (email: string) => {
  try {
    const response = await apiClient.post('/users/password-reset/', { email });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Reset password error:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}
export const changePassword = async (current_password: string, new_password: string, new_password_confirm: string, logout_all_session?: boolean) => {
  try {
    const response = await apiClient.post('/users/change-password/', {
      current_password,
      new_password,
      new_password_confirm,
      logout_all_sessions: logout_all_session ?? true
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Change password error:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const resetPasswordConfirm = async (email: string, verification_code: string, password: string, password_confirm: string) => {
  try {
    const response = await apiClient.post('/users/password-reset-confirm/', {
      email,
      verification_code,
      password,
      password_confirm
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Reset password confirm error:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}
export const updateProfile = async (name: string, file: File) => {
  try {
    const formData = new FormData();
    formData.append('name', name);
    formData.append('image', file);

    const response = await apiClient.put('/users/profile/', formData);

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update profile');
    }

    return await response.json();
  } catch (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
};

export const viewProfile = async () => {
  try {
    const response = await apiClient.get('/users/profile');
    return await response.json();
  } catch (error) {
    console.error('Error viewing profile:', error);
    throw error;
  }
}
export const deleteAccount = async () => {
  try {
    const response = await apiClient.delete('/users/delete-account');
    return await response.json();
  } catch (error) {
    console.error('Error deleting account:', error);
    throw error;
  }
}

export const googleAuth = async (idToken: string) => {
  try {
    const response = await apiClient.post('/auth/google/', { id_token: idToken });
    const data = await response.json();

    if (!response.ok) {
      console.error('Google auth failed:', data);
      return {
        status: response.status,
        data
      };
    }

    // If authentication is successful, store both tokens
    if (data.tokens?.access) {
      setAccessToken(data.tokens.access);

      // Store refresh token if available
      if (data.tokens?.refresh) {
        setRefreshToken(data.tokens.refresh);
      }
    }

    return {
      status: response.status,
      data
    };
  } catch (error) {
    console.error('Google auth error:', error);
    return {
      status: 500,
      data: { error: 'Network error' }
    };
  }
};

//Stay

export const locationAllLocations = async () => {
  try {
    const response = await apiClient.get('/stay/locations');
    return await response.json();
  } catch (error) {
    console.error('Error fetching locations:', error);
    return [];
  }
}

export const locationGetLocation = async (location_id: string) => {
  try {
    const response = await apiClient.get(`/stay/locations/${location_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error fetching location ${location_id}:`, error);
    return null;
  }
}

export const locationCreaLocation = async (street: string, post_code: string, city: string, country: string, latitude: number, longitude: number) => {
  try {
    const response = await apiClient.post('/stay/locations/', {
      street,
      post_code,
      city,
      country,
      latitude,
      longitude
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating location:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}
export const locationUpdateLocation = async (location_id: string, street: string, post_code: string, city: string, country: string, latitude: number, longitude: number) => {
  try {
    const response = await apiClient.put(`/stay/locations/${location_id}`, {
      street,
      post_code,
      city,
      country,
      latitude,
      longitude
    });

    return await response.json();
  } catch (error) {
    console.error(`Error updating location ${location_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const propertyCreateProperty = async (name: string, property_type: number, description: string, location: string, relation_type: string, is_multi_unit: boolean) => {
  try {
    const response = await apiClient.post('/stay/property/', {
      name,
      property_type,
      description,
      location,
      relation_type,
      active_rooms: 1,
      is_multi_unit
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating property:', error);
    return { error: 'Network error occurred' };
  }
}

export const propertyAllProperties = async () => {
  try {
    const response = await apiClient.get('/stay/property');
    return await response.json();
  } catch (error) {
    console.error('Error fetching properties:', error);
    return [];
  }
}
export const propertyUpdateProperty = async (property_id: string, name: string, property_type: number, description: string) => {
  try {
    const response = await apiClient.patch(`/stay/property/${property_id}/`, {
      name,
      property_type,
      description
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error updating property ${property_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const propertyDeleteProperty = async (property_id: string) => {
  try {
    const response = await apiClient.delete(`/stay/property/${property_id}/`);
    return await response.json();
  } catch (error) {
    console.error(`Error deleting property ${property_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const metadataViewPropertyData = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/stay/property-metadata?property=${property_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error fetching property metadata for ${property_id}:`, error);
    return null;
  }
}
export const metadataModifyPropertyInfo = async (property_id: string, regional_id_code: string) => {
  try {
    const response = await apiClient.patch(`/stay/property-metadata/${property_id}/property-info/`, {
      regional_id_code,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error modifying property info for ${property_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const metadataCheckInCheckOut = async (property_id: string, check_in_time_from: string, check_in_time_untill: string, check_out_time: string) => {
  try {
    const response = await apiClient.patch(`/stay/property-metadata/${property_id}/checkin-checkout/`, {
      check_in_time_from,
      check_in_time_untill,
      check_out_time
    });

    return await response.json();
  } catch (error) {
    console.error(`Error updating check-in/check-out for ${property_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const metadataCancellationPolicy = async (property_id: string, cancellation_policy_type: string) => {
  try {
    const response = await apiClient.patch(`/stay/property-metadata/${property_id}/cancellation-policy/`, {
      cancellation_policy_type
    });

    return await response.json();
  } catch (error) {
    console.error(`Error updating cancellation policy for ${property_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const amenitiesCreateAndUpdate = async (property_id: string, amenities: { name: string, category: string, is_available: boolean }[]) => {
  try {
    const response = await apiClient.post('/stay/amenity/', {
      property: property_id,
      amenities: amenities
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error creating/updating amenities for property ${property_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const amenitiesPropertyAmenities = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/stay/amenity/?property=${property_id}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error fetching amenities for property ${property_id}:`, error);
    return {
      data: [],
      status: 500
    };
  }
}

export const roomCreateRoom = async (
  property: string,
  room_type: string,
  room_rate: string,
  max_occupancy: number,
  max_child_occupancy: number,
  size_measurement: number | string,
  size_measurement_unit: string,
  description: string,
  quantity: number = 1,
  bathroom_quantity: number = 1,
  bed_config?: {[key: string]: any}
) => {
  try {
    // Format bed_config data to ensure bed_type_id is a number
    let formattedBedConfig: {[key: string]: {beds: Array<{bed_type_id: number, bed_count: number}>, roomSpaceType: string}} | undefined = undefined;

    if (bed_config) {
      formattedBedConfig = {};
      Object.entries(bed_config).forEach(([roomKey, roomConfig]) => {
        // Ensure roomConfig.beds exists and is an array
        const beds = Array.isArray(roomConfig.beds) ? roomConfig.beds : [];
        interface Bed {
          bed_type_id: string | number;
          bed_count: number;
        }

        interface BedConfig {
          beds: Bed[];
          roomSpaceType: string;
        }

        interface FormattedBedConfig {
          [key: string]: BedConfig;
        }

        interface RoomConfig {
          beds: Bed[];
          roomSpaceType: string;
        }

                formattedBedConfig![roomKey] = {
                  beds: beds.map((bed: Bed) => ({
                    bed_type_id: typeof bed.bed_type_id === 'string' ? parseInt(bed.bed_type_id) : Number(bed.bed_type_id),
                    bed_count: bed.bed_count
                  })),
                  roomSpaceType: roomConfig.roomSpaceType || 'bedroom'
                };
      });
    }

    const response = await apiClient.post('/stay/rooms/', {
      property,
      room_type: parseInt(room_type),
      room_rate: parseFloat(room_rate),
      max_occupancy,
      quantity: 1, // Always force quantity to 1
      max_child_occupancy,
      size_measurement: typeof size_measurement === 'string' ? parseFloat(size_measurement) : size_measurement,
      size_measurement_unit,
      description,
      bed_config: formattedBedConfig,
      bathroom_quantity
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating room:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const roomsListAllRoomsProperty = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/stay/rooms?property=${property_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error fetching rooms for property ${property_id}:`, error);
    return [];
  }
}
interface Bed {
  bed_type_id: string | number;
  bed_count: number;
}

interface RoomConfig {
  beds: Bed[];
  roomSpaceType: string;
}

export const roomUpdateRoom = async (
  room_id: string,
  property: string,
  room_type: string,
  room_rate: string,
  max_occupancy: number,
  max_child_occupancy: number,
  size_measurement: number | string,
  description: string,
  quantity: number = 1,
  bathroom_quantity: number = 1,
  bed_config?: {[key: string]: any}
) => {
  try {
    // Format bed_config data to ensure bed_type_id is a number
    let formattedBedConfig: {[key: string]: RoomConfig} | undefined = undefined;

    if (bed_config) {
      formattedBedConfig = {};
      Object.entries(bed_config).forEach(([roomKey, roomConfig]) => {
        // Ensure roomConfig.beds exists and is an array
        const beds = Array.isArray(roomConfig.beds) ? roomConfig.beds : [];
        formattedBedConfig![roomKey] = {
          beds: beds.map((bed: Bed) => ({
            bed_type_id: typeof bed.bed_type_id === 'string' ? parseInt(bed.bed_type_id) : Number(bed.bed_type_id),
            bed_count: bed.bed_count
          })),
          roomSpaceType: roomConfig.roomSpaceType || 'bedroom'
        };
      });
    }

    // Rest of the function remains the same...
    const response = await apiClient.put(`/stay/rooms/${room_id}/`, {
      property,
      room_type: parseInt(room_type),
      room_rate: parseFloat(room_rate),
      max_occupancy,
      max_child_occupancy,
      size_measurement: typeof size_measurement === 'string' ? parseFloat(size_measurement) : size_measurement,
      description,
      size_measurement_unit: 'sqm',
      bed_config: formattedBedConfig,
      quantity: 1, // Always force quantity to 1
      bathroom_quantity
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error updating room ${room_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const roomActivateDeactivateRoom = async (room_id: string, is_active: boolean) => {
  try {
    const response = await apiClient.patch(`/stay/rooms/${room_id}`, { is_active });
    return await response.json();
  } catch (error) {
    console.error(`Error ${is_active ? 'activating' : 'deactivating'} room ${room_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const roomDeleteRoom = async (room_id: string) => {
  try {
    const response = await apiClient.delete(`/stay/rooms/${room_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error deleting room ${room_id}:`, error);
    return { error: 'Network error occurred' };
  }
}
export const roomCreateAndUpadateAmenities = async (room_id: string, amenities: { name: string, category: string, is_available: boolean }[]) => {
  try {
    const response = await apiClient.post('/stay/room-amenities', {
      room: room_id,
      amenities: amenities
    });

    return await response.json();
  } catch (error) {
    console.error(`Error creating/updating amenities for room ${room_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const roomListRoomAmenities = async (room_id: string) => {
  try {
    const response = await apiClient.get(`/stay/room-amenities?room=${room_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error fetching amenities for room ${room_id}:`, error);
    return [];
  }
}

export const arrivalCreateGuestArrival = async (property: string, contact_name: string, contact_surname: string, email: string, phone_number: string) => {
  try {
    const response = await apiClient.post('/stay/guest-info/', {
      property,
      contact_name,
      contact_surname,
      email,
      phone_number
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating guest arrival:', error);
    return { error: 'Network error occurred' };
  }
}

export const arrivalViewGuestArrival = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/stay/guest-info?property=${property_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error viewing guest arrival for property ${property_id}:`, error);
    return [];
  }
}

export const arrivalUpdateGuestArrival = async (guest_id: string, property: string, contact_name: string, contact_surname: string, email: string, phone_number: string) => {
  try {
    const response = await apiClient.put(`/stay/guest-info/${guest_id}/`, {
      property,
      contact_name,
      contact_surname,
      email,
      phone_number
    });

    return await response.json();
  } catch (error) {
    console.error(`Error updating guest arrival ${guest_id}:`, error);
    return { error: 'Network error occurred' };
  }
}
export const arrivalDeleteGuestArrival = async (guest_id: string, contact_name: string, contact_surname: string, email: string, phone_number: string, key_pickup_instructions: string, arrival_instructions: string) => {
  try {
    const response = await apiClient.delete(`/stay/guest-info/${guest_id}`, {
      contact_name,
      contact_surname,
      email,
      phone_number,
      key_pickup_instructions,
      arrival_instructions
    });

    return await response.json();
  } catch (error) {
    console.error(`Error deleting guest arrival ${guest_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const photosUploadPhotos = async (property: string, photos: File[]) => {
  try {
    const formData = new FormData();

    formData.append('property', property);
    photos.forEach(photo => {
      formData.append('image', photo);
    });

    const response = await apiClient.post('/stay/photos', formData);
    return await response.json();
  } catch (error) {
    console.error('Error uploading photos:', error);
    return { error: 'Network error occurred' };
  }
}

export const photosDeletePhoto = async (photo_ids: string[]) => {
  try {
    const response = await apiClient.delete('/stay/photos/bulk-delete', {
      ids: photo_ids
    });

    return await response.json();
  } catch (error) {
    console.error('Error deleting photos:', error);
    return { error: 'Network error occurred' };
  }
}

//Property
export const getSettingsBubbleStatus = async (propertyId: string) => {
  try {
    const response = await apiClient.get(`/stay/settings-status/?property=${propertyId}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error getting settings bubble status for property ${propertyId}:`, error);
    return {
      data: null,
      status: 500
    };
  }
}

export const viewPropertyBookings = async (property_id: string, start_date: string, end_date: string) => {
  try {
    const response = await apiClient.get(`/booking/property/${property_id}?&start_date=${start_date}&end_date=${end_date}`);
    return await response.json();
  } catch (error) {
    console.error(`Error viewing bookings for property ${property_id}:`, error);
    return [];
  }
}

export const viewPropertyBlockedDates = async (property_id: string, start_date?: string, end_date?: string) => {
  try {
    // Build endpoint with optional date filters
    let endpoint = `/booking/booking-blocks/?property=${property_id}`;
    if (start_date && end_date) {
      endpoint += `&start_date=${start_date}&end_date=${end_date}`;
    }

    const response = await apiClient.get(endpoint);
    return await response.json();
  } catch (error) {
    console.error(`Error viewing blocked dates for property ${property_id}:`, error);
    return [];
  }
}

export const deactivateBookingBlocks = async (block_id: string) => {
  try {
    const response = await apiClient.post(`/booking/booking-blocks/${block_id}/deactivate/`, {});
    return await response.json();
  } catch (error) {
    console.error(`Error deactivating booking block ${block_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const createRoomRate = async (property: string, rate_plan: number, rate: string, start_date: string, end_date: string, minimum_stay: number, maximum_stay: number) => {
  try {
    const response = await apiClient.post('/pricing/room-rates/', {
      rate_plan: rate_plan,
      room: property,
      rate,
      room_amount: '1',
      start_date,
      end_date,
      minimum_stay,
      maximum_stay,
      is_season: true, //! Why hardcoded?? <AUTHOR> Lacchini
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating room rate:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const getPropertyRates = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/pricing/room-rates/?property=${property_id}`);
    return await response.json();
  } catch (error) {
    console.error(`Error getting property rates for ${property_id}:`, error);
    return [];
  }
}
export const createBookingBlock = async (property_id: string, start_date: string, end_date: string, reason: string) => {
  try {
    const response = await apiClient.post('/booking/booking-blocks/', {
      property: property_id,
      start_date,
      end_date,
      reason
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating booking block:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const createManualBooking = async (property_id: string, customer: any, reservation_data: any) => {
  try {
    const response = await apiClient.post('/booking/manual-booking/', {
      property: property_id,
      customer,
      reservation_data
    });

    return await response.json();
  } catch (error) {
    console.error('Error creating manual booking:', error);
    return { error: 'Network error occurred' };
  }
}

export const addPictureToProperty = async (property_id: string, pictures: File[]) => {
  try {
    const formData = new FormData();
    const pictureArray = Array.from(pictures);

    formData.append('property', property_id);
    pictureArray.forEach(photo => {
      formData.append('image', photo);
    });

    const response = await apiClient.post('/stay/photos/', formData);
    const data = await response.json();

    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Error uploading pictures:', error);
    return {
      data: { message: 'Network error occurred during upload' },
      status: 500
    };
  }
}
export const deletePictureFromProperty = async (photo_id: string) => {
  try {
    const response = await apiClient.delete('/stay/photos/bulk-delete/', {
      ids: [photo_id]
    });

    const data = await response.json();

    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Error deleting picture:', error);
    return {
      data: { message: 'Network error occurred during deletion' },
      status: 500
    };
  }
}

export const requestBookingCancelation = async (booking_id: string, reason: string) => {
  try {
    const response = await apiClient.post(`/booking/cancellation-request/`, {booking_id, reason});
    return await response.json();
  } catch (error) {
    console.error(`Error requesting booking cancelation for ${booking_id}:`, error);
    return { error: 'Network error occurred' };
  }
}

export const getAllBookings = async (page: number = 1, size: number = 10) => {
  try {
    const response = await apiClient.get(`/booking/property/?page=${page}&page_size=${size}`);
    const data = await response.json();

    return {
      results: data.results || [],
      count: data.count || 0,
      next: data.next || null,
      previous: data.previous || null,
      currentPage: page,
      totalPages: data.count ? Math.ceil(data.count / size) : 0,
      pageSize: size
    };
  } catch (error) {
    console.error('Error getting all bookings:', error);
    return {
      results: [],
      count: 0,
      next: null,
      previous: null,
      currentPage: page,
      totalPages: 0,
      pageSize: size
    };
  }
}

export const getMonthDailyRate = async (propertyId: string, year: string, month: string) => {
  try {
    const formattedMonth = month.padStart(2, '0');
    const response = await apiClient.get(`/pricing/room-rates/calendar/?property=${propertyId}&year=${year}&month=${formattedMonth}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error getting daily rates for property ${propertyId}:`, error);
    return {
      data: null,
      status: 500
    };
  }
}


export const viewAllRatePlans = async (property: string) => {
  try {
    const response = await apiClient.get(`/pricing/rate-plans/?property=${property}`);
    return await response.json();
  } catch (error) {
    console.error(`Error viewing rate plans for property ${property}:`, error);
    return [];
  }
}

export const createPropertyRatePlan = async (property: string, name: string, close_out_days: number, close_out_time: string, checkin_time: string, description: string, meal_plan: number) => {
  try {
    const response = await apiClient.post('/pricing/rate-plans/', {
      property,
      name,
      close_out_days,
      close_out_time,
      checkin_time,
      description,
      meal_plan
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating property rate plan:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const updatePropertyRatePlan = async (property: string, name: string, close_out_days: number, close_out_time: string, checkin_time: string, description: string, meal_plan: number, ratePlanId: string) => {
  try {
    const response = await apiClient.put(`/pricing/rate-plans/${ratePlanId}/`, {
      property,
      name,
      close_out_days,
      close_out_time,
      checkin_time,
      description,
      meal_plan
    });

    return response.status;
  } catch (error) {
    console.error(`Error updating rate plan ${ratePlanId}:`, error);
    return 500;
  }
}


export const viewCollaborators = async (propertyId: string) => {
  try {
    const response = await apiClient.get(`/stay/team/property_staff/?property_id=${propertyId}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error viewing collaborators for property ${propertyId}:`, error);
    return {
      data: [],
      status: 500
    };
  }
}

export const viewPropertyInvites = async (propertyId: string) => {
  try {
    const response = await apiClient.get(`/stay/team/property_invites/?property_id=${propertyId}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error viewing invites for property ${propertyId}:`, error);
    return {
      data: [],
      status: 500
    };
  }
}

export const inviteCollaborator = async (
  property_id: string,
  email: string,
  permissions: []
) => {
  try {
    const response = await apiClient.post('/stay/team/', {
      property_id,
      email,
      permissions,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error inviting collaborator:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const viewInvite = async (invite_id: string) => {
  try {
    const response = await apiClient.get(`/stay/team/verify_invite/?id=${invite_id}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error viewing invite ${invite_id}:`, error);
    return {
      data: null,
      status: 500
    };
  }
}

export const teamSignup = async (invite_id: string, name: string, email: string, phone: string, password: string) => {
  try {
    const response = await apiClient.post('/users/team/signup/', {
      invite_id,
      name,
      email,
      phone,
      password
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error signing up team member:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const acceptInvite = async (invite_id: string) => {
  try {
    const response = await apiClient.post('/stay/team/accept_invite/', {
      id: invite_id,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error accepting invite ${invite_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}




export const updateCollaborator = async (
  property_id: string,
  user_id: string,
  permissions: []
) => {
  try {
    const response = await apiClient.put('/stay/team/update_permissions/', {
      property_id,
      user_id,
      permissions,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error updating collaborator ${user_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const removeCollaborator = async (
  property_id: string,
  user_id: string
) => {
  try {
    const response = await apiClient.delete('/stay/team/remove_staff/', {
      property_id,
      user_id,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error removing collaborator ${user_id}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}



//Notifications
export const getNotifications = async () => {
  try {
    const response = await apiClient.get('/integrations/notifications/');
    return await response.json();
  } catch (error) {
    console.error('Error getting notifications:', error);
    return [];
  }
}

//Billing
export const getBilling = async () => {
  try {
    const response = await apiClient.get('/billing/billing-profiles/');

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error getting billing profiles:', error);
    return {
      data: [],
      status: 500
    };
  }
}

export const getBillingAddress = async () => {
  try {
    const response = await apiClient.get('/billing/billing-addresses/');

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error getting billing addresses:', error);
    return {
      data: [],
      status: 500
    };
  }
}

export const getBillingTaxes = async () => {
  try {
    const response = await apiClient.get('/billing/taxation/');

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error getting billing taxes:', error);
    return {
      data: [],
      status: 500
    };
  }
}

export const createBilling = async (
  name: string,
  surname: string,
  date_of_birth: string,
  nationanility: string,
  recipient_type: string,
  gender: string,
  id_document: File,
  iban: string
) => {
  try {
    const formData = new FormData();

    formData.append('first_name', name);
    formData.append('last_name', surname);
    formData.append('date_of_birth', date_of_birth);
    formData.append('nationality', nationanility);
    formData.append('recipient_type', recipient_type);
    formData.append('gender', gender);
    formData.append('id_document', id_document);
    formData.append('iban', iban);

    const response = await apiClient.post('/billing/billing-profiles/', formData);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating billing profile:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const createTaxesBilling = async (
  //has_vat_number: boolean,
  tin_number: string,
  tin_country: string,
  rent_more_than_4_properties: boolean
) => {
  try {
    const response = await apiClient.post('/billing/taxation/', {
      has_vat_number: false,
      tin_number,
      tin_country,
      rent_more_than_4_properties
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating taxes billing:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}
export const createAddressBilling = async (
  street_number: string,
  postcode: string,
  city: string,
  country: string,
) => {
  try {
    const response = await apiClient.post('/billing/billing-addresses/', {
      street_number,
      postcode,
      city,
      country,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error creating billing address:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const updateBilling = async (params: {
  iban: string;
  billingProfileId?: string;
  name?: string;
  surname?: string;
  date_of_birth?: string;
  nationality?: string;
  gender?: string;
}) => {
  // Validate IBAN format
  const cleanIban = params.iban.replace(/\s/g, '');
  // Basic IBAN validation - should start with country code and have correct length
  const ibanRegex = /^[A-Z]{2}\d{2}[A-Z0-9]{10,30}$/;

  if (!ibanRegex.test(cleanIban)) {
    return {
      success: false,
      data: { error: 'Formato IBAN non valido' },
      status: 400
    };
  }

  try {
    const formData = new FormData();

    // Always include IBAN
    formData.append('iban', cleanIban);

    // Only include other fields if they are provided
    if (params.name) formData.append('first_name', params.name);
    if (params.surname) formData.append('last_name', params.surname);
    if (params.date_of_birth) formData.append('date_of_birth', params.date_of_birth);
    if (params.nationality) formData.append('nationality', params.nationality);
    if (params.gender) formData.append('gender', params.gender);

    // Get the billing profile ID from the API if not provided
    let billingProfileId = params.billingProfileId;
    if (!billingProfileId) {
      const billingResponse = await getBilling();
      if (billingResponse.status === 200 && billingResponse.data && billingResponse.data.length > 0) {
        billingProfileId = billingResponse.data[0].id;
      } else {
        return {
          success: false,
          data: { error: 'Impossibile trovare il profilo di fatturazione' },
          status: 404
        };
      }
    }

    const response = await apiClient.patch(`/billing/billing-profiles/${billingProfileId}/`, formData);
    const data = await response.json();

    return {
      success: response.ok,
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Error updating billing information:', error);
    return {
      success: false,
      data: { error: 'Si è verificato un errore durante l\'aggiornamento delle informazioni bancarie' },
      status: 500
    };
  }
}
export const updateTaxesBilling = async (
  //has_vat_number: boolean,
  tin_number: string,
  tin_country: string,
  rent_more_than_4_properties: boolean,
  taxationPlanId: string
) => {
  try {
    const response = await apiClient.patch(`/billing/taxation/${taxationPlanId}/`, {
      has_vat_number: false,
      tin_number,
      tin_country,
      rent_more_than_4_properties
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error updating taxes billing for plan ${taxationPlanId}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const updateAddressBilling = async (
  street_number: string,
  postcode: string,
  city: string,
  country: string,
  addressPlanId: string
) => {
  try {
    const response = await apiClient.patch(`/billing/billing-addresses/${addressPlanId}/`, {
      street_number,
      postcode,
      city,
      country: country,
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error updating address billing for plan ${addressPlanId}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const getReviews = async () => {
  try {
    const response = await apiClient.get('/reviews/');
    return await response.json();
  } catch (error) {
    console.error('Error getting reviews:', error);
    return [];
  }
}

export const getPropertyReviews = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/reviews/?property=${property_id}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error getting reviews for property ${property_id}:`, error);
    return {
      data: [],
      status: 500
    };
  }
}

export const getPropertyReviewsStats = async (property_id: string) => {
  try {
    const response = await apiClient.get(`/reviews/stats/?property=${property_id}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error getting review stats for property ${property_id}:`, error);
    return {
      data: null,
      status: 500
    };
  }
}

export const handleSyncProperty = async (property_id: string) => {
  try {
    const response = await apiClient.post(`/integrations/sync-property/${property_id}/`, {});

    return {
      status: response.status
    };
  } catch (error) {
    console.error(`Error syncing property ${property_id}:`, error);
    return {
      status: 500
    };
  }
}

export const getInvoices = async () => {
  try {
    const response = await apiClient.get('/integrations/invoice/');

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error getting invoices:', error);
    return {
      data: [],
      status: 500
    };
  }
}
export const getPayouts = async (start_date: string, end_date: string) => {
  try {
    const response = await apiClient.get(`/integrations/payouts/?start_date=${start_date}&end_date=${end_date}`);

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error getting payouts:', error);
    return {
      data: [],
      status: 500
    };
  }
}
export const signContract = async () => {
  try {
    const response = await apiClient.post('/integrations/create-connected-account/', {
      tos_accepted: true
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error('Error signing contract:', error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}


/* Templates */
export interface Template {
  id: number;
  title: string;
  description: string;
  file: string;
  category: string;
  created_at: string;
}

export const getTemplates = async (): Promise<Template[]> => {
  try {
    const response = await apiClient.get('/integrations/templates/');
    if (!response.ok) {
      throw new Error('Failed to fetch templates');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching templates:', error);
    return [];
  }
};

/* Chat */
export const sendChatMessage = async (message: string, attachments: File[] | null) => {
  // Validate message - ensure it's not empty or just whitespace when there are no attachments
  if ((!message || !message.trim()) && (!attachments || attachments.length === 0)) {
    return {
      success: false,
      error: 'Il messaggio o gli allegati non possono essere vuoti'
    };
  }

  const formData = new FormData();

  // Use the trimmed message to remove leading/trailing whitespace
  formData.append('message', message.trim());

  if (attachments) {
    attachments.forEach((file) => {
      formData.append(`attachments`, file);
    });
  }
  try {
    const response = await apiClient.post('/support/messages/', formData);
    const data = await response.json();

    return {
      success: response.ok,
      data,
      status: response.status
    };
  } catch (error) {
    console.error('Error sending chat message:', error);
    return {
      success: false,
      error: 'Si è verificato un errore durante l\'invio del messaggio'
    };
  }
}
export const getChatMessages = async (url?: string) => {
  try {
    const endpoint = url || '/support/messages/';
    const response = await apiClient.get(endpoint);
    return await response.json();
  } catch (error) {
    console.error('Error getting chat messages:', error);
    return { count: 0, next: null, previous: null, results: [] };
  }
}


/* SU */
export const propertyOnBoarding = async (propertyId: string) => {
  try {
    const response = await apiClient.post('/integrations/onboard-property/', {
      property_id: propertyId
    });

    return response.status;
  } catch (error) {
    console.error(`Error onboarding property ${propertyId}:`, error);
    return 500;
  }
}

/* Online Check-in API Functions */
// Cache for online check-in status to reduce API calls
const onlineCheckInStatusCache: Record<string, { data: any, timestamp: number }> = {};

// Cache duration in milliseconds (2 minutes)
const STATUS_CACHE_DURATION = 2 * 60 * 1000;

export const getPropertyOnlineCheckInStatus = async (propertyId: string, forceRefresh = false) => {
  // Return cached data if available and not expired
  if (
    !forceRefresh &&
    onlineCheckInStatusCache[propertyId] &&
    Date.now() - onlineCheckInStatusCache[propertyId].timestamp < STATUS_CACHE_DURATION
  ) {
    return {
      data: onlineCheckInStatusCache[propertyId].data,
      status: 200,
      cached: true
    };
  }

  try {
    const response = await apiClient.get(`/integrations/online-checkin/status/${propertyId}/`);
    const data = await response.json();

    // Cache the result
    onlineCheckInStatusCache[propertyId] = {
      data,
      timestamp: Date.now()
    };

    return {
      data,
      status: response.status,
      cached: false
    };
  } catch (error) {
    console.error(`Error getting online check-in status for property ${propertyId}:`, error);

    // If we have cached data, return it even if expired
    if (onlineCheckInStatusCache[propertyId]) {
      return {
        data: onlineCheckInStatusCache[propertyId].data,
        status: 200,
        cached: true,
        stale: true
      };
    }

    // Otherwise return default data
    return {
      data: {
        is_enabled: false,
        istat_connected: false,
        alloggati_connected: false
      },
      status: 500,
      cached: false
    };
  }
}

// Cache for online check-in configuration to reduce API calls
const onlineCheckInConfigCache: Record<string, { data: any, timestamp: number }> = {};

// Cache duration in milliseconds (5 minutes)
const CONFIG_CACHE_DURATION = 5 * 60 * 1000;

export const getPropertyOnlineCheckInConfig = async (propertyId: string, forceRefresh = false) => {
  // Return cached data if available and not expired
  if (
    !forceRefresh &&
    onlineCheckInConfigCache[propertyId] &&
    Date.now() - onlineCheckInConfigCache[propertyId].timestamp < CONFIG_CACHE_DURATION
  ) {
    return {
      data: onlineCheckInConfigCache[propertyId].data,
      status: 200,
      cached: true
    };
  }

  try {
    const response = await apiClient.get(`/integrations/online-checkin/config/${propertyId}/`);
    const data = await response.json();

    // Cache the result
    onlineCheckInConfigCache[propertyId] = {
      data,
      timestamp: Date.now()
    };

    return {
      data,
      status: response.status,
      cached: false
    };
  } catch (error) {
    console.error(`Error getting online check-in config for property ${propertyId}:`, error);

    // If we have cached data, return it even if expired
    if (onlineCheckInConfigCache[propertyId]) {
      return {
        data: onlineCheckInConfigCache[propertyId].data,
        status: 200,
        cached: true,
        stale: true
      };
    }

    // Otherwise return default data
    return {
      data: {
        is_enabled: false,
        istat_enabled: false,
        alloggati_enabled: false
      },
      status: 500,
      cached: false
    };
  }
}

export const savePropertyOnlineCheckInConfig = async (propertyId: string, config: any) => {
  try {
    const response = await apiClient.post(`/integrations/online-checkin/config/${propertyId}/`, config);
    const data = await response.json();

    // Clear the cache for this property to ensure fresh data on next fetch
    if (onlineCheckInConfigCache[propertyId]) {
      delete onlineCheckInConfigCache[propertyId];
    }

    if (onlineCheckInStatusCache[propertyId]) {
      delete onlineCheckInStatusCache[propertyId];
    }

    return {
      data,
      status: response.status
    };
  } catch (error) {
    console.error(`Error saving online check-in config for property ${propertyId}:`, error);
    return {
      data: { error: 'Network error occurred' },
      status: 500
    };
  }
}

export const testOnlineCheckInConnection = async (propertyId: string, connectionType: 'istat' | 'alloggati') => {
  try {
    const response = await apiClient.post(`/integrations/online-checkin/test-connection/${propertyId}/`, {
      connection_type: connectionType
    });

    return {
      data: await response.json(),
      status: response.status
    };
  } catch (error) {
    console.error(`Error testing ${connectionType} connection for property ${propertyId}:`, error);
    return {
      data: { error: 'Network error occurred', success: false },
      status: 500
    };
  }
}

