'use client'
import { PropertyProvider } from '@/components/_context/PropertyContext'
import { UserProvider } from '@/components/_context/UserContext'
import { CalendarProvider } from '@/components/_context/CalendarContext'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <UserProvider>
      <PropertyProvider>
        <CalendarProvider>
          {children}
        </CalendarProvider>
      </PropertyProvider>
    </UserProvider>
  )
}
