"use client"
import { usePathname, useRouter } from 'next/navigation';
import React, { createContext, useState, useEffect, useCallback } from 'react';
import { deleteToken, verifyToken, login, logout as apiLogout } from '@/services/api';
import { initializeFromStorage, isTokenExpired, getAccessToken, getRefreshToken } from '@/services/tokenService';
import { initializeAuthentication } from '@/services/apiInterceptor';

interface AuthContextType {
    isAuthenticated: boolean;
    isLoading: boolean;
    isSidepanelOpen: boolean;
    setIsSidepanelOpen: (boolean: boolean) => void;
    login: (email: string, password: string) => Promise<boolean>;
    logout: () => Promise<void>;
    checkAuth: () => Promise<boolean>;
}

export const AuthContext = createContext<AuthContextType>({
    isAuthenticated: false,
    isLoading: true,
    isSidepanelOpen: false,
    setIsSidepanelOpen: () => {},
    login: async () => false,
    logout: async () => {},
    checkAuth: async () => false,
});

interface AuthProviderProps {
    children: React.ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
    const pathname = usePathname();
    const router = useRouter();
    const [isSidepanelOpen, setIsSidepanelOpen] = useState<boolean>(true);
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);

    // Initialize token from localStorage on first render (for backward compatibility)
    useEffect(() => {
        initializeFromStorage();
        checkAuthentication();
    }, []);

    // Check if the user is authenticated
    const checkAuthentication = useCallback(async () => {
        setIsLoading(true);

        try {
            // First check if we have a valid access token
            const hasAccessToken = !!getAccessToken();
            const hasRefreshToken = !!getRefreshToken();

            // If we have an access token and it's not expired, we're authenticated
            if (hasAccessToken && !isTokenExpired()) {
                setIsAuthenticated(true);
                setIsLoading(false);
                return true;
            }

            // If we have a refresh token, try to get a new access token
            // but only if we're not in a cooldown period
            if (hasRefreshToken) {
                try {
                    // This will use the cooldown mechanism internally
                    const initialized = await initializeAuthentication();
                    if (initialized) {
                        setIsAuthenticated(true);
                        setIsLoading(false);
                        return true;
                    }
                } catch (error) {
                    console.error('Token refresh failed:', error);
                    // Continue to check with the server
                }
            }

            // If we have an access token (even if expired), verify with server as last resort
            if (hasAccessToken) {
                const call = await verifyToken();
                const isValid = call.status === 200;

                if (!isValid) {
                    deleteToken();
                    setIsAuthenticated(false);
                } else {
                    setIsAuthenticated(true);
                }

                setIsLoading(false);
                return isValid;
            }

            // No valid tokens
            setIsAuthenticated(false);
            setIsLoading(false);
            return false;
        } catch (error) {
            console.error('Authentication check failed:', error);
            setIsAuthenticated(false);
            setIsLoading(false);
            return false;
        }
    }, []);

    // Handle login
    const handleLogin = async (email: string, password: string): Promise<boolean> => {
        setIsLoading(true);
        const response = await login(email, password);

        if (response.status === 200) {
            setIsAuthenticated(true);
            setIsLoading(false);
            return true;
        }

        setIsAuthenticated(false);
        setIsLoading(false);
        return false;
    };

    // Handle logout
    const handleLogout = async (): Promise<void> => {
        setIsLoading(true);
        await apiLogout();
        setIsAuthenticated(false);
        setIsLoading(false);
        router.push('/auth/login');
    };

    // Check authentication on protected routes
    useEffect(() => {
        const pieces = pathname.split('/');
        const protectedRoutes = ['/dashboard'];

        if (pieces.length > 1 && protectedRoutes.some(route => pathname.startsWith(route))) {
            const handleVerifyToken = async () => {
                const isValid = await checkAuthentication();

                if (!isValid) {
                    const redirectPath = encodeURIComponent(pathname);
                    router.push(`/auth/login?redirect=${redirectPath}`);
                }
            };

            handleVerifyToken();
        }
    }, [pathname, router, checkAuthentication]);

    return (
        <AuthContext.Provider
            value={{
                isAuthenticated,
                isLoading,
                isSidepanelOpen,
                setIsSidepanelOpen,
                login: handleLogin,
                logout: handleLogout,
                checkAuth: checkAuthentication
            }}
        >
            {children}
        </AuthContext.Provider>
    );
};