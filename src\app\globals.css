@tailwind base;
@tailwind components;
@tailwind utilities;
  
  
  :root {
    --background: #FFFFFF;
    --card-background: #EBEBED;
    
    --text-faded: #8C8C8D;
    --text: #202021;
    

    --accent: #FCB51F;
    --blue: #133157;


    --green: #4CAF50;
    --red: #F44336;

    --text: #fff;
    --text-secondary: #a1a1a1;

    --status-background: #222;
    --dots-color: #222;
    --cup-color: #fff;
    --steam-color: #333;

    --text-xl: 1.5rem;
    --text-lg: 1.25rem;
    --text-md: 1rem;
    --text-sm: 0.875rem;
    --text-xs: 0.75rem;

  }


* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  overflow-y: none;
}

html,
body {
  max-width: 100vw;
  height: 100dvh;
  overflow-y: none;
  background-color: var(--background);
  scrollbar-width: 0px;
  scrollbar-color: transparent transparent;
  -webkit-text-size-adjust: 100%;
  
}

body {
  color: rgb(var(--foreground-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}



@media (max-width: 600px) {
  input {
    font-size: 16px; /* Make sure the font size is not too small for mobile */
  }
}