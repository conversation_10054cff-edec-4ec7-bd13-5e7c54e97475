'use client'
import React, { useContext, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { UserContext } from '@/components/_context/UserContext'
import { PropertyContext } from '@/components/_context/PropertyContext'
import Loader1 from '@/components/loaders/Loader1'
import toast from 'react-hot-toast'
import Button from '@/components/buttons/Button'
import InputOnOff from '@/components/inputs/inputOnOff'
import CardAlert from '@/components/cards/CardAlert'
import { getPropertyOnlineCheckInConfig, savePropertyOnlineCheckInConfig } from '@/services/api'

function Page({ params }: { params: { propertyId: string } }) {
  const router = useRouter()
  const { isNotOwnerPermission, isCustomer, hasBillingProfile } = useContext(UserContext)
  const { properties, isLoadingProperties, fetchProperties } = useContext(PropertyContext)

  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [property, setProperty] = useState<any>(null)

  const [config, setConfig] = useState({
    is_enabled: false,
    istat_enabled: false,
    alloggati_enabled: false
  })

  // Check permissions
  useEffect(() => {
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile) {
      toast.error('Non hai i permessi necessari per accedere a questa sezione')
      router.push('/dashboard/settings/account')
    }
  }, [isNotOwnerPermission, isCustomer, hasBillingProfile, router])

  // Fetch properties only once on component mount
  useEffect(() => {
    // Skip if we don't have permissions
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile) {
      return
    }

    // Fetch properties if needed
    fetchProperties()
  }, [fetchProperties, isNotOwnerPermission, isCustomer, hasBillingProfile])

  // Process properties and fetch configuration when properties are loaded
  useEffect(() => {
    // Skip if we don't have permissions or if properties are still loading
    if (isNotOwnerPermission || !isCustomer || !hasBillingProfile || isLoadingProperties) {
      return
    }

    // Skip if properties array is empty
    if (!properties || properties.length === 0) {
      return
    }

    const loadPropertyData = async () => {
      setIsLoading(true)
      try {
        // Find the current property
        const currentProperty = properties.find(p => p.id === params.propertyId)
        if (!currentProperty) {
          toast.error('Proprietà non trovata')
          router.push('/dashboard/settings/account/online-check-in')
          return
        }

        // Check if property is onboarded and active
        if (!currentProperty.is_onboarded || !currentProperty.is_active) {
          toast.error('Questa proprietà non è attiva o non è stata completamente configurata')
          router.push('/dashboard/settings/account/online-check-in')
          return
        }

        setProperty(currentProperty)

        // Get the online check-in configuration
        const response = await getPropertyOnlineCheckInConfig(params.propertyId)
        if (response.status === 200) {
          setConfig(response.data)
        } else {
          // If API fails, use default empty config
          console.error('Failed to load configuration, using defaults')
        }
      } catch (error) {
        console.error('Error loading property data:', error)
        toast.error('Errore durante il caricamento dei dati della proprietà')
      } finally {
        setIsLoading(false)
      }
    }

    loadPropertyData()
  }, [properties, isLoadingProperties, params.propertyId, router, isNotOwnerPermission, isCustomer, hasBillingProfile])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // No validation needed since we removed username/password fields

      // Save configuration
      const response = await savePropertyOnlineCheckInConfig(params.propertyId, config)

      if (response.status === 200 || response.status === 201) {
        toast.success('Configurazione salvata con successo')

        // Notify the user that the configuration has been saved
        if (config.istat_enabled) {
          toast.success('Connessione ISTAT attivata. Il nostro team di supporto completerà la configurazione.')
        }

        if (config.alloggati_enabled) {
          toast.success('Connessione Alloggati attivata. Il nostro team di supporto completerà la configurazione.')
        }

        router.push('/dashboard/settings/account/online-check-in')
      } else {
        toast.error(response.data.error || 'Errore durante il salvataggio della configurazione')
      }
    } catch (error) {
      console.error('Error saving configuration:', error)
      toast.error('Errore durante il salvataggio della configurazione')
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <MobilePageStart>
      <HeaderPage
        title='Configura Check-in Online'
        actionLeftIcon={() => router.push('/dashboard/settings/account/online-check-in')}
        actionRightIcon={() => router.push('/dashboard/settings')}
      />

      {isLoading || isLoadingProperties ? (
        <div className="flex-1 flex items-center justify-center">
          <Loader1 />
        </div>
      ) : (
        <div className='mt-4 px-4 h-full w-full flex flex-col gap-6 pb-20'>
          {/* Property Info */}
          <div>
            <h2 className='text-xl font-semibold mb-2'>{property?.name}</h2>
            <p className='text-gray-600 text-sm'>
              Configura le connessioni per l'invio automatico dei dati degli ospiti alle autorità.
            </p>
          </div>

          {/* Enable Online Check-in */}
          <div className='bg-white rounded-lg p-4 shadow-sm border border-gray-200'>
            <div className='flex justify-between items-center'>
              <div>
                <h3 className='font-medium'>Attiva Check-in Online</h3>
                <p className='text-sm text-gray-600 mt-1'>
                  Abilita l'invio automatico dei dati degli ospiti
                </p>
              </div>
              <InputOnOff
                title="Attiva Check-in Online"
                value={config.is_enabled}
                onChange={(value) => setConfig({...config, is_enabled: value})}
              />
            </div>
          </div>

          {/* ISTAT Connection */}
          <div className='bg-white rounded-lg p-4 shadow-sm border border-gray-200'>
            <div className='flex justify-between items-center mb-4'>
              <div>
                <h3 className='font-medium'>Connessione ISTAT</h3>
                <p className='text-sm text-gray-600 mt-1'>
                  Configura la connessione al portale ISTAT
                </p>
              </div>
              <InputOnOff
                title="Attiva ISTAT"
                value={config.istat_enabled}
                onChange={(value) => setConfig({...config, istat_enabled: value})}
              />
            </div>

            {config.istat_enabled && (
              <div className='space-y-4 mt-4'>
                <p className='text-sm text-gray-600'>
                  La connessione ISTAT verrà configurata automaticamente dal nostro team di supporto.
                  Una volta attivata, i dati degli ospiti verranno inviati automaticamente al portale ISTAT.
                </p>
              </div>
            )}
          </div>

          {/* Alloggati Web Connection */}
          <div className='bg-white rounded-lg p-4 shadow-sm border border-gray-200'>
            <div className='flex justify-between items-center mb-4'>
              <div>
                <h3 className='font-medium'>Connessione Alloggati Web</h3>
                <p className='text-sm text-gray-600 mt-1'>
                  Configura la connessione al portale Alloggati Web
                </p>
              </div>
              <InputOnOff
                title="Attiva Alloggati"
                value={config.alloggati_enabled}
                onChange={(value) => setConfig({...config, alloggati_enabled: value})}
              />
            </div>

            {config.alloggati_enabled && (
              <div className='space-y-4 mt-4'>
                <p className='text-sm text-gray-600'>
                  La connessione Alloggati Web verrà configurata automaticamente dal nostro team di supporto.
                  Una volta attivata, i dati degli ospiti verranno inviati automaticamente al portale Alloggati Web.
                </p>
              </div>
            )}
          </div>

          {/* Warning Card */}
          <CardAlert
            title='Importante'
            message="Assicurati di inserire credenziali corrette. Credenziali errate potrebbero causare problemi nell'invio dei dati alle autorità."
            color='orange'
          />

          {/* Save Button */}
          <div className='fixed bottom-0 left-0 right-0 bg-white p-4 border-t border-gray-200'>
            <Button
              text={isSaving ? "Salvataggio in corso..." : "Salva Configurazione"}
              backgroundColor="#113158"
              color="white"
              fontSize="16px"
              className="w-full"
              onClick={handleSave}
              disabled={isSaving}
            />
          </div>
        </div>
      )}
    </MobilePageStart>
  )
}

export default Page
