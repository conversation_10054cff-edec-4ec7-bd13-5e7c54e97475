'use client'
import React, { useContext, useEffect } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { ReviewsContext } from '@/components/_context/ReviewsContext'
import CardReview from '@/components/cards/CardReview'
import Loader1 from '@/components/loaders/Loader1'
import { useRouter } from 'next/navigation'

function ReviewsPage() {
    const { reviews, isLoadingReviews, fetchReviews } = useContext(ReviewsContext)
    const router = useRouter()
    useEffect(() => {
        fetchReviews()
    }, [])

    return (
        <MobilePageStart isNavbar>
            <HeaderPage
                title='Recensioni'
                actionLeftIcon={() => {
                    router.back()
                }}  
                actionRightIcon={()=> {
                    router.push('/dashboard')
                }}
            />
            <div className="px-4 py-6">                
                {isLoadingReviews ? (
                    <div className='h-full w-full flex items-center justify-center'>
                        <Loader1/>
                    </div>
                ) : reviews.length > 0 ? (
                    <div className="space-y-4">
                        {reviews.map((review) => (
                            <CardReview
                                key={review.id}
                                publicReview={review.public_review}
                                submittedAt={review.submitted_at}
                                reviewerRole={review.reviewer_role}
                            />
                        ))}
                    </div>
                ) : (
                    <div className="text-center text-gray-500">
                        Nessuna recensione ancora
                    </div>
                )}
            </div>
        </MobilePageStart>
    )
}

export default ReviewsPage
