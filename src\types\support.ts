export interface SupportMessage {
  id: string;
  chat_id: string;
  message: string;
  sender: 'user' | 'support';
  created_at: string;
  user_name: string;
  attachments: SupportAttachment[];
  is_read?: boolean; // Added to match the API response
}

export interface SupportAttachment {
  id: string;
  file_url: string;
  file_name: string;
  file_size?: number; // Size in bytes
  content_type?: string; // MIME type
  created_at: string;
}

export interface WebSocketMessage {
  type: 'chat.message' | 'ping' | 'pong' | 'error' | 'typing.start' | 'typing.stop' | 
        'file.uploaded' | 'upload.progress' | 'file.upload.progress' | 'file.upload.notify' | 
        'client.disconnect';
  message?: string;
  sender?: 'user' | 'support';
  timestamp?: string;
  id?: string;
  chat_id?: string;
  user_name?: string;
  attachments?: SupportAttachment[];  user_id?: string;
  message_id?: string;
  client_message_id?: string; // To track messages from client to server
  progress?: number;
  action?: 'start' | 'stop';
  is_support?: boolean;
  is_read?: boolean;
}

export interface ConnectionStatus {
  isConnected: boolean;
  error?: string;
  reconnectAttempts?: number;
}

export interface ChatPaginationResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: SupportMessage[];
}

export interface TypingStatus {
  isTyping: boolean;
  userIds: string[];
}

export interface PendingMessage {
  id: string;
  message: string;
  attachments: File[];
  timestamp: string;
  status: 'sending' | 'sent' | 'confirmed' | 'error';
  clientMessageId?: string; // To track the client-side ID for WebSocket messages
  serverMessageId?: string; // To store the server-assigned message ID when confirmed
}
