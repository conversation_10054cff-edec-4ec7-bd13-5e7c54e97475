"use client"
import React, { createContext, useState, useCallback, useEffect } from 'react';
import { propertyAllProperties } from '@/services/api';

interface PropertyContextType {
    properties: any[];
    isLoadingProperties: boolean;
    fetchProperties: () => Promise<void>;
    lastFetchTime: number | null;
}

export const PropertyContext = createContext<PropertyContextType>({
    properties: [],
    isLoadingProperties: true,
    fetchProperties: async () => {},
    lastFetchTime: null
});

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

export const PropertyProvider = ({ children }: { children: React.ReactNode }) => {
    const [properties, setProperties] = useState<any[]>([]);
    const [isLoadingProperties, setIsLoadingProperties] = useState<boolean>(true);
    const [lastFetchTime, setLastFetchTime] = useState<number | null>(null);

    // Memoize the fetchProperties function to prevent it from changing on every render
    const fetchProperties = useCallback(async (forceRefresh = false) => {
        // Skip fetching if we have recent data and forceRefresh is false
        if (!forceRefresh && lastFetchTime && Date.now() - lastFetchTime < CACHE_DURATION && properties.length > 0) {
            return;
        }

        setIsLoadingProperties(true);
        try {
            const call = await propertyAllProperties();
            setProperties(call);
            setLastFetchTime(Date.now());
        } catch (error) {
            console.error('Error fetching properties:', error);
        } finally {
            setIsLoadingProperties(false);
        }
    }, [lastFetchTime, properties.length]);

    // Fetch properties on initial load
    useEffect(() => {
        fetchProperties();
    }, []);

    return (
        <PropertyContext.Provider value={{ properties, isLoadingProperties, fetchProperties, lastFetchTime }}>
            {children}
        </PropertyContext.Provider>
    );
};
