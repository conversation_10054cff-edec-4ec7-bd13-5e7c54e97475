# Heibooky Web Application

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Prerequisites

Before you begin, ensure you have the following installed:

- Node.js (version 16.x or higher)
- npm (comes with Node.js) or yarn/pnpm/bun
- Git (for version control)

## Installation

1 Clone the repository:

```bash
git clone https://github.com/Heibooky/web-app.git
cd web-app
```

2 Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
# or
bun install
```

## Development

Start the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

### Key Development Commands

- `npm run build` - Build the application for production
- `npm start` - Start the production server
- `npm run lint` - Run ESLint for code quality
- `npm test` - Run tests (if configured)

## Environment Variables

Create a `.env.local` file in the root directory using the `.env.example` as a template:

```env
NEXT_PUBLIC_API_URL=your_api_url
# Add other environment variables
```

## Best Practices

- Keep components small and reusable
- Follow the file-based routing convention
- Use TypeScript for better type safety
- Implement proper error handling
- Follow Next.js best practices for image optimization

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Learn Next.js](https://nextjs.org/learn)
- [Next.js GitHub Repository](https://github.com/vercel/next.js/)

## Deployment

Deploy your Next.js app using [Vercel](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme):

1. Push your code to a Git repository
2. Import your project into Vercel
3. Vercel will detect Next.js automatically and deploy with optimal settings

For other deployment options, check the [Next.js deployment documentation](https://nextjs.org/docs/deployment).

## Contributing

1. Fork/Clone the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Open a Pull Request
