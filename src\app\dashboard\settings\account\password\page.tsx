'use client'
import React, { useState } from 'react'
import { useRouter } from 'next/navigation'
import { EyeIcon, EyeOffIcon } from "lucide-react"
import Link from 'next/link'
import { changePassword } from '@/services/api'
import toast from 'react-hot-toast'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'

interface FormData {
  currentPassword: string;
  newPassword: string;
  repeatPassword: string;
}

interface FormErrors {
  currentPassword?: string;
  newPassword?: string;
  repeatPassword?: string;
}

interface ErrorMessageProps {
  message?: string;
}

function PasswordChangePage() {
  const router = useRouter()
  const [formData, setFormData] = useState<FormData>({
    currentPassword: '',
    newPassword: '',
    repeatPassword: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showRepeatPassword, setShowRepeatPassword] = useState(false)

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const validateForm = () => {
    const newErrors: FormErrors = {}
    
    // Check for required fields
    if (!formData.currentPassword.trim()) {
      newErrors.currentPassword = 'La password corrente è obbligatoria'
    }
    
    if (!formData.newPassword.trim()) {
      newErrors.newPassword = 'La nuova password è obbligatoria'
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = 'La password deve contenere almeno 8 caratteri'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*])/.test(formData.newPassword)) {
      newErrors.newPassword = 'La password deve contenere almeno una lettera maiuscola, minuscola, un numero e un carattere speciale'
    }
    
    if (!formData.repeatPassword.trim()) {
      newErrors.repeatPassword = 'Ripeti la password è obbligatoria'
    } else if (formData.newPassword !== formData.repeatPassword) {
      newErrors.repeatPassword = 'Le password non corrispondono'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleChangePassword = async () => {
    if (!validateForm()) return
    
    setIsSubmitting(true)
    try {
      const response = await changePassword(
        formData.currentPassword,
        formData.newPassword,
        formData.repeatPassword
      )

      if (response.status === 200) {
        toast.success('Password cambiata con successo')
        setTimeout(() => {
          router.push('/dashboard/')
        }, 1000)
      } else {
        toast.error('La password corrente è errata')
      }
    } catch (error: any) {
      const errorMessage = error?.response?.detail || 'Errore nel cambio della password'
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const ErrorMessage: React.FC<ErrorMessageProps> = ({ message }) => (
    message ? <p className="text-red-500 text-sm mt-1">{message}</p> : null
  )

  return (
    <MobilePageStart>
      <HeaderPage 
        title='Modifica Password'
        actionLeftIcon={() => router.back()}
        actionRightIcon={() => router.push('/dashboard/settings')}
      />

      <div className='mt-6 px-4 h-full w-full flex flex-col justify-between'>
        <div className='flex flex-col gap-6'>
          <div className='mb-2'>
        <InputLabelDescription
          label='Password Corrente'
          type={showCurrentPassword ? "text" : "password"}
          placeholder='Inserisci la password corrente'
          value={formData.currentPassword}
          onChange={(value) => handleInputChange('currentPassword', value)}
          required
          icon={
            <button type="button" onClick={() => setShowCurrentPassword(!showCurrentPassword)}>
          {showCurrentPassword ? (
            <EyeOffIcon size={18} />
          ) : (
            <EyeIcon size={18} />
          )}
            </button>
          }
        />
        <ErrorMessage message={errors.currentPassword} />
          </div>

          <div className='mb-2'>
        <InputLabelDescription
          label='Nuova Password'
          type={showNewPassword ? "text" : "password"}
          placeholder='Es. P4ssw0rD!'
          value={formData.newPassword}
          onChange={(value) => handleInputChange('newPassword', value)}
          description="Usa almeno 8 caratteri con lettere maiuscole, minuscole, numeri e simboli"
          required
          icon={
            <button type="button" onClick={() => setShowNewPassword(!showNewPassword)}>
            {showNewPassword ? (
              <EyeOffIcon size={18} />
            ) : (
              <EyeIcon size={18} />
            )}
            </button>
          }
        />
        <ErrorMessage message={errors.newPassword} />
          </div>

          <div className='mb-2'>
        <InputLabelDescription
          label='Ripeti Password'
          type={showRepeatPassword ? "text" : "password"}
          placeholder='Ripeti la nuova password'
          value={formData.repeatPassword}
          onChange={(value) => handleInputChange('repeatPassword', value)}
          required
          icon={
            <button type="button" onClick={() => setShowRepeatPassword(!showRepeatPassword)}>
          {showRepeatPassword ? (
            <EyeOffIcon size={18} />
          ) : (
            <EyeIcon size={18} />
          )}
            </button>
          }
        />
        <ErrorMessage message={errors.repeatPassword} />
          </div>
        </div>

        <div className='mt-8 mb-6'>
          <Button
        color='white'
        backgroundColor='var(--blue)'
        text={isSubmitting ? 'Caricamento...' : 'Conferma Modifiche'}
        onClick={handleChangePassword}
        disabled={isSubmitting}
        className="w-full py-3 font-medium"
          />
          <Link href='/auth/recover'>
            <p
              className='text-center mt-4'
              style={{
                color: 'var(--accent)',
                textDecoration: 'underline',
                fontSize: '14px'
              }}
            >
              Password dimenticata?
            </p>
          </Link>
        </div>
      </div>
    </MobilePageStart>
  )
}

export default PasswordChangePage