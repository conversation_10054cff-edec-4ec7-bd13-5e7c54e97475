{"name": "fe-heib<PERSON>y", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.1.5", "@react-oauth/google": "^0.12.1", "@types/js-cookie": "^3.0.6", "date-fns": "^4.1.0", "expo-device": "^7.0.2", "framer-motion": "^12.6.3", "js-cookie": "^3.0.5", "leaflet": "^1.9.4", "libphonenumber-js": "^1.12.6", "lottie-react": "^2.4.1", "lucide-react": "^0.468.0", "next": "^14.2.27", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.1.0", "react-phone-number-input": "^3.4.12", "react-quill": "^2.0.0"}, "devDependencies": {"@types/leaflet": "^1.9.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5"}}