'use client'
import React, { useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import Navbar from '@/components/_globals/navbar'
import HeaderPage from '@/components/_globals/headerPage'
import { useRouter } from 'next/navigation'
import Title from '@/components/titles/Title'
import Card from '@/components/cards/Card'
import Button from '@/components/buttons/Button'
import Image from 'next/image'
import { getTemplates } from '@/services/api'
import { Template } from '@/services/api'
import Loader1 from '@/components/loaders/Loader1'

function Page() {
  const router = useRouter()
  const [templates, setTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [downloadingIds, setDownloadingIds] = useState<Set<number>>(new Set())

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const data = await getTemplates();
        console.log('Templates fetched:', data); // Add logging for debugging
        setTemplates(data || []); // Ensure we always set an array
      } catch (error) {
        console.error('Error fetching templates:', error);
        setTemplates([]); // Set empty array on error
      } finally {
        setLoading(false);
      }
    }

    fetchTemplates();
  }, [])

  const handleDownload = async (fileUrl: string, title: string, templateId: number) => {
    setDownloadingIds(prev => new Set(prev).add(templateId))
    
    try {
      // Use our proxy API route to bypass CORS
      const proxyUrl = `/api/download?url=${encodeURIComponent(fileUrl)}&filename=${encodeURIComponent(title + '.pdf')}`;
      
      const response = await fetch(proxyUrl, {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Failed to download file')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `${title}.pdf`
      document.body.appendChild(link)
      link.click()
      
      // Cleanup
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Error downloading file:', error)
      alert('Errore durante il download del file')
    } finally {
      setDownloadingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(templateId)
        return newSet
      })
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'contract':
        return '📄'
      case 'rules':
        return '📋'
      default:
        return '📎'
    }
  }

  return (
    <MobilePageStart>
      <HeaderPage
        title='Templates'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push('/dashboard')
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-4'>
        <Title
          title='I nostri Templates'
          subtitle='Scarica i nostri template per gestire al meglio la tua proprietà'
        />

        {loading ? (
          <div className="flex justify-center items-center h-32">
            <Loader1 />
          </div>
        ) : (
          templates.map((template) => (
            <Card
              key={template.id}
              style={{
                width: '100%',
                background: 'white',
                padding: '20px',
                alignItems: 'flex-start',
                gap: '15px'
              }}
            >
              <div className="flex items-center w-full">
                <div className="flex-shrink-0 mr-4">
                  <Image
                    src="/icons/pdf.svg"
                    alt="PDF"
                    width={40}
                    height={40}
                  />
                </div>
                <div className="flex-grow">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getCategoryIcon(template.category)}</span>
                    <h3 className="text-[16px] font-semibold">{template.title}</h3>
                  </div>
                  <p className="text-[12px] text-gray-600 mt-1">{template.description}</p>
                </div>
              </div>
              <Button
                text={downloadingIds.has(template.id) ? "Scaricando..." : "Scarica PDF"}
                color="white"
                backgroundColor="var(--blue)"
                onClick={() => handleDownload(template.file, template.title, template.id)}
                fontSize="12px"
                className="w-full mt-2"
                disabled={downloadingIds.has(template.id)}
                icon={
                  downloadingIds.has(template.id) ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                      <polyline points="7 10 12 15 17 10" />
                      <line x1="12" y1="15" x2="12" y2="3" />
                    </svg>
                  )
                }
              />
            </Card>
          ))
        )}

        {!loading && templates.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            Nessun template disponibile al momento
          </div>
        )}
      </div>
      <Navbar />
    </MobilePageStart>
  )
}

export default Page
