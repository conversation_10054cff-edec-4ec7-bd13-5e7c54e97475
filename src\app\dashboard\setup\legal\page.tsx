'use client'
import React, { useEffect, useState } from 'react'
import MobilePage from '@/components/_globals/mobilePage'

import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import MultiSelect from '@/components/inputs/multiSelect'
import ButtonLoading from '@/components/buttons/ButtonLoading'
import { useRouter } from 'next/navigation'
import { propertyCreateProperty } from '@/services/api'
import toast from 'react-hot-toast'


function Page() {
  const router = useRouter()
  const [selectedOption, setSelectedOption] = useState(['owner'])

  const handleSaveAndContinue = async () => {
    //Save in localstorage
    localStorage.setItem('legalRelation-crp', selectedOption[0])

    return handleCreateProperty()
  }

  const clearLocalStorageData = () => {
    // Clear all property creation related data
    localStorage.removeItem('name-crp')
    localStorage.removeItem('description-crp')
    localStorage.removeItem('propertyMode-crp')
    localStorage.removeItem('locationId')
    localStorage.removeItem('legalRelation-crp')
    localStorage.removeItem('propertyType-crp')
    localStorage.removeItem('guests-crp')
  }

  const handleCreateProperty = async () => {
    //1. Get Name
    const name = localStorage.getItem('name-crp')

    //2. Description
    const description = localStorage.getItem('description-crp')

    //3. Property Mode
    const propertyMode = localStorage.getItem('propertyMode-crp')

    //4. LocationId
    const locationId = localStorage.getItem('locationId')

    //5. Legal Relation
    const legalRelation = localStorage.getItem('legalRelation-crp') ?? selectedOption[0]

    //6. Property Type
    const propertyType = localStorage.getItem('propertyType-crp')

    //7. Guests
    const guests = localStorage.getItem('guests-crp')

    if (!name || !description || !propertyMode || !locationId || !legalRelation || !propertyType || !guests) {
      toast.error('Errore: Dati mancanti')
      return false
    }

    try {
      const response = await propertyCreateProperty(
        name,
        Number(propertyType),
        description,
        locationId,
        legalRelation,
        propertyMode === '2' ? true : false
      )

      if (response.error) {
        toast.error('Errore nella creazione della proprieta')
        return false
      } else {
        toast.success('Proprieta creata con successo')
        clearLocalStorageData() // Clear localStorage after successful creation
        router.push('/dashboard')
        return true
      }
    } catch (error) {
      console.error('Error creating property:', error)
      toast.error('Errore durante la creazione della proprieta')
      return false
    }
  }


  return (
    <MobilePageStart>
      <HeaderPage title='Relazione legale'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push('/dashboard')
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col justify-between'>
        <MultiSelect
          title='Relazione legale rispetto alla proprieta?'
          isMultiSelect={false}
          options={[
            { id: 'owner', title: 'Proprietario Legale', },
            { id: 'tenant_admin', title: 'Amministratore Legale' },
            { id: 'intermediary_admin', title: 'Amministratore Intermediario' },
          ]}
          value={selectedOption}
          onChange={(selected) => setSelectedOption(selected)}
        />

        <ButtonLoading
          color='white'
          backgroundColor='var(--blue)'
          text='Continua'
          onClick={handleSaveAndContinue}
        />
      </div>


    </MobilePageStart>
  )
}

export default Page