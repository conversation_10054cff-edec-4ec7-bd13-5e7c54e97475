import React from 'react'
import styles from './card.module.css'

interface CardProps {
    children: React.ReactNode
    className? : any
    style?: React.CSSProperties
    onClick?: () => void
}

function Card({ children, className, style, onClick }: CardProps) {
  return (
    <div onClick={onClick} className={`${className} ${styles.card}`} style={style}>
        {children}
    </div>
  )
}

export default Card