'use client'
import React, { useEffect, useState, useRef, useContext, useCallback } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import { useRouter } from 'next/navigation'
import { sendChatMessage } from '@/services/api'
import HeaderPage from '@/components/_globals/headerPage'
import CardAlert from '@/components/cards/CardAlert'
import toast from 'react-hot-toast'
import { useSupportChat } from '@/hooks/useSupportChat'
import { isImageFile, downloadFile } from '@/utils/fileUtils'
import { SupportMessage, PendingMessage, SupportAttachment } from '@/types/support'
import { SupportChatErrorBoundary } from '@/components/chat/SupportChatErrorBoundary'
import { AttachmentList } from '@/components/chat/AttachmentPreview'
import { ImagePreviewModal } from '@/components/chat/ImagePreviewModal'
import { TypingIndicator } from '@/components/chat/TypingIndicator'
import { ConnectionStatusIndicator } from '@/components/chat/ConnectionStatusIndicator'
import { EnhancedMessageInput } from '@/components/chat/EnhancedMessageInput'
import { AuthContext } from '@/components/_context/AuthContext'
import { getAccessToken } from '@/services/tokenService'
import Loader1 from '@/components/loaders/Loader1'
import { FileUploadService, FileUploadProgress } from '@/services/FileUploadService'

function Page() {
  // Existing state
  const router = useRouter()
  const { isAuthenticated, isLoading } = useContext(AuthContext)
  const [message, setMessage] = useState<string>('')
  const [attachments, setAttachments] = useState<File[] | null>(null)
  const [pendingMessages, setPendingMessages] = useState<PendingMessage[]>([])
  const [sentAttachmentUrls, setSentAttachmentUrls] = useState<{[key: string]: string}>({})
  const [previewImage, setPreviewImage] = useState<{attachment: SupportMessage['attachments'][0], url: string} | null>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  // New ref to track previous messages for comparison
  const previousMessagesRef = useRef<SupportMessage[]>([]);
  
  // New state for file uploads
  const [fileUploads, setFileUploads] = useState<FileUploadProgress[]>([])
  const fileUploadServiceRef = useRef<FileUploadService | null>(null)
    // Get user token - only after authentication is complete
  const userToken = isAuthenticated && !isLoading ? getAccessToken() : null
    // Handle message confirmation from server
  const handleMessageConfirmed = useCallback((clientMessageId: string, serverMessageId: string) => {
    console.log(`Message confirmed by server: ${clientMessageId} → ${serverMessageId}`);
    
    // Update the pending message status to confirmed
    setPendingMessages(prev => {
      // Find the pending message that matches this client ID
      const pendingMessage = prev.find(pm => pm.clientMessageId === clientMessageId);
      if (!pendingMessage) {
        return prev; // No matching pending message found
      }
      
      // Just mark it as confirmed but keep it visible until it appears in main list
      // The monitoring useEffect will handle removing it when it appears in the main list
      return prev.map(pm => 
        pm.clientMessageId === clientMessageId 
          ? { ...pm, status: 'confirmed', serverMessageId } 
          : pm
      );
    });
  }, []); // No dependencies needed
  
  const {
    messages,
    connectionStatus,
    sendMessage: sendWebSocketMessage,
    loadMore,
    hasMore,
    isLoadingMore,
    chatId,
    isInitializing,
    typingStatus,
    handleUserTyping,
    wsRef  // Get WebSocket reference for FileUploadService
  } = useSupportChat({
    token: userToken || '',
    enabled: isAuthenticated && !isLoading && !!userToken,
    onMessageConfirmed: handleMessageConfirmed
  });  
  
  // Define the progress update handler with useCallback to avoid recreating on every render
  const handleProgressUpdate = useCallback((progress: FileUploadProgress) => {
    setFileUploads(prev => {
      // Check if this progress update already exists to avoid duplicates
      const exists = prev.some(p => 
        p.fileName === progress.fileName && 
        p.messageId === progress.messageId &&
        p.progress === progress.progress
      );
      if (exists) return prev;
      return [...prev, progress];
    });
  }, []);

  // Initialize FileUploadService when WebSocket is ready
  useEffect(() => {
    if (wsRef.current && connectionStatus.isConnected && !fileUploadServiceRef.current && chatId) {
      fileUploadServiceRef.current = new FileUploadService({
        chatId: chatId,
        webSocketService: wsRef.current,
        onProgressUpdate: handleProgressUpdate
      });
    }
    
    return () => {
      // Just set to null since FileUploadService doesn't have a cleanup method
      fileUploadServiceRef.current = null;
    };
  }, [wsRef, connectionStatus.isConnected, chatId, handleProgressUpdate]);
    // Monitor messages and clean up pending messages when they appear in the main message list
  useEffect(() => {
    // Skip if no messages
    if (messages.length === 0) return;
    
    // Compare with previous messages to avoid unnecessary updates
    const hasNewMessages = messages.length !== previousMessagesRef.current.length || 
      messages.some((msg, index) => previousMessagesRef.current[index]?.id !== msg.id);
    
    if (hasNewMessages) {
      // Update our ref to the current messages
      previousMessagesRef.current = [...messages];
      
      // Check if any pending messages have corresponding messages in the main list
      setPendingMessages(currentPending => {
        if (currentPending.length === 0) return currentPending;
        
        return currentPending.filter(pending => {
          // Keep the message if it doesn't have a server ID yet
          if (!pending.serverMessageId) return true;
          
          // Check if this pending message has appeared in the main message list
          const isInMainList = messages.some(msg => msg.id === pending.serverMessageId);
          
          // If it's in the main list, we can remove it from pending
          return !isInMainList;
        });
      });
    }
  }, [messages]); // Only depend on messages array
  // Separate effect for auto-scrolling
  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive or pending messages change
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages.length, pendingMessages.length]); // Only depend on the length of arrays
    // Cleanup blob URLs when component unmounts or pending messages change
  useEffect(() => {
    return () => {
      // Cleanup blob URLs for pending messages
      pendingMessages.forEach(pendingMsg => {
        if (pendingMsg.attachments) {
          pendingMsg.attachments.forEach(file => {
            // Since we create URLs in the render, we need to be careful about cleanup
            // The URLs are created in the render function and should be cleaned up there
          });
        }
      });
    };
  }, []); // Remove dependency on pendingMessages to avoid circular updates

  // Handle file upload with WebSocket progress tracking
  const handleFileUpload = useCallback(async (file: File, messageId?: string): Promise<boolean> => {
    if (!fileUploadServiceRef.current) {
      console.error('FileUploadService not initialized');
      return false;
    }
    
    try {
      await fileUploadServiceRef.current.uploadFile(file, messageId);
      return true;
    } catch (error) {
      console.error('File upload failed:', error);
      toast.error(`Errore durante l'upload del file: ${file.name}`);
      return false;
    }
  }, []);

  // Enhanced message sending with proper WebSocket and file handling
  const sendMessage = async (messageText: string, attachments: File[] | null) => {
    // Don't send empty messages (unless they have attachments)
    if ((!messageText || !messageText.trim()) && (!attachments || attachments.length === 0)) {
      return;
    }
    
    // Generate pending message ID
    const pendingId = `pending-${Date.now()}`;
    
    // Create pending message object for optimistic UI
    const pendingMessage: PendingMessage = {
      id: pendingId,
      message: messageText.trim(),
      attachments: attachments || [],
      timestamp: new Date().toISOString(),
      status: 'sending'
    };

    // Add to pending messages immediately
    setPendingMessages(prev => [...prev, pendingMessage]);

    // Create attachment URLs for display
    const currentAttachmentUrls: {[key: string]: string} = {};
    if (attachments) {
      attachments.forEach(file => {
        const url = URL.createObjectURL(file);
        currentAttachmentUrls[file.name] = url;
      });
    }

    // Update sent attachment URLs
    setSentAttachmentUrls({...sentAttachmentUrls, ...currentAttachmentUrls});

    // Clear input immediately for better UX
    const currentMessage = messageText;
    const currentAttachments = attachments ? [...attachments] : null;
    setMessage('');
    setAttachments(null);

    try {
      // Case 1: Text-only message and WebSocket is available - use WebSocket
      if (connectionStatus.isConnected && chatId && messageText && (!currentAttachments || currentAttachments.length === 0)) {
        console.log('Sending text-only message via WebSocket');
        const result = sendWebSocketMessage(messageText);
        
        if (result.success) {
          const msgId = result.messageId;
          // Update pending message status
          setPendingMessages(prev => 
            prev.map(pm => pm.id === pendingId ? { 
              ...pm, 
              status: 'sent', 
              clientMessageId: msgId || undefined
            } : pm)
          );
          
          console.log(`Message sent with tracking ID: ${msgId}`);
        } else {
          console.error('Failed to send message via WebSocket');
          // Fall back to API
          handleApiSendFallback(pendingId, messageText, null);
        }
      }
      // Case 2: Message with attachments - use API with WebSocket progress tracking
      else if (currentAttachments && currentAttachments.length > 0) {
        console.log('Sending message with attachments via API');
        const response = await sendChatMessage(messageText, currentAttachments);
        
        if (response?.success && response?.data) {
          // Message sent successfully, update UI
          const serverMessageId = response.data.id;
          
          setPendingMessages(prev => 
            prev.map(pm => pm.id === pendingId ? { 
              ...pm, 
              status: 'sent',
              serverMessageId: serverMessageId
            } : pm)
          );
          
          console.log('Message with attachments sent via API:', serverMessageId);
          
          // If WebSocket is connected, track upload progress via WebSocket
          if (connectionStatus.isConnected && wsRef.current && currentAttachments.length > 0) {
            console.log('Setting up WebSocket tracking for file uploads');
            
            // Associate files with the message for WebSocket tracking
            currentAttachments.forEach(file => {
              if (fileUploadServiceRef.current) {
                handleFileUpload(file, serverMessageId);
              }
            });
          }
        } else {
          console.error('Failed to send message with attachments');
          setPendingMessages(prev => 
            prev.map(pm => pm.id === pendingId ? { ...pm, status: 'error' } : pm)
          );
          toast.error('Errore durante l\'invio del messaggio');
        }
      }
      // Case 3: No WebSocket but have text - use API
      else {
        handleApiSendFallback(pendingId, messageText, null);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      setPendingMessages(prev => 
        prev.map(pm => pm.id === pendingId ? { ...pm, status: 'error' } : pm)
      );
      toast.error('Errore durante l\'invio del messaggio');
    }
  };
  
  // Helper function for API fallback
  const handleApiSendFallback = async (pendingId: string, messageText: string, files: File[] | null) => {
    console.log('Using API fallback for message sending');
    try {
      const response = await sendChatMessage(messageText, files);
      
      if (response?.success && response?.data) {
        setPendingMessages(prev => 
          prev.map(pm => pm.id === pendingId ? { 
            ...pm, 
            status: 'sent',
            serverMessageId: response.data.id 
          } : pm)
        );
      } else {
        setPendingMessages(prev => 
          prev.map(pm => pm.id === pendingId ? { ...pm, status: 'error' } : pm)
        );
        toast.error('Errore durante l\'invio del messaggio');
      }
    } catch (error) {
      console.error('API fallback failed:', error);
      setPendingMessages(prev => 
        prev.map(pm => pm.id === pendingId ? { ...pm, status: 'error' } : pm)
      );
      toast.error('Errore durante l\'invio del messaggio');
    }
  };

  const handleSendMessage = async () => {
    // Use our enhanced sendMessage function
    await sendMessage(message, attachments);
  };

  const renderPendingMessage = (pendingMsg: PendingMessage) => {
    return (
      <div
        key={pendingMsg.id}
        className="w-full flex justify-end mb-3"
      >        <div
          style={{
            maxWidth: '70%',
            padding: '10px 20px',
            borderRadius: '20px 0 20px 20px',
            boxShadow: '0px 0px 10px 0px #00000010',
            backgroundColor: 'var(--accent)',
            color: 'white',
            opacity: pendingMsg.status === 'confirmed' ? 1 : (pendingMsg.status === 'error' ? 0.6 : 0.8),
            border: pendingMsg.status === 'error' ? '2px solid #ef4444' : 
                   (pendingMsg.status === 'confirmed' ? '2px solid #10b981' : 'none')
          }}
        >
          {/* Message text */}
          {pendingMsg.message && (
            <p className="mb-2" style={{ 
              color: 'white',
              whiteSpace: 'pre-wrap'
            }}>
              {pendingMsg.message}
            </p>
          )}

          {/* Status indicator */}
          <div className="flex items-center justify-between">
            <p className="text-xs" style={{
              color: 'rgba(255,255,255,0.7)'
            }}>
              {new Date(pendingMsg.timestamp).toLocaleString('it-IT', {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
            
            <div className="flex items-center ml-2">
              {pendingMsg.status === 'sending' && (
                <div className="flex space-x-1">
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-1 h-1 bg-white rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
              )}              {pendingMsg.status === 'sent' && (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
              {pendingMsg.status === 'confirmed' && (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              )}
              {pendingMsg.status === 'error' && (
                <svg className="w-4 h-4 text-red-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>          {/* Pending attachments */}
          {pendingMsg.attachments && pendingMsg.attachments.length > 0 && (
            <div className="mt-2">
              <AttachmentList
                attachments={pendingMsg.attachments.map(file => ({
                  id: `pending-${file.name}`,
                  file_name: file.name,
                  file_size: file.size,
                  file_type: file.type,
                  file_url: URL.createObjectURL(file),
                  created_at: new Date().toISOString()
                }))}
                sentAttachmentUrls={Object.fromEntries(
                  pendingMsg.attachments.map(file => [file.name, URL.createObjectURL(file)])
                )}
                isUserMessage={true}
                layout={pendingMsg.attachments.length > 2 ? 'grid' : 'horizontal'}
                size="medium"
                onAttachmentClick={(attachment, url) => {
                  if (isImageFile(attachment.file_name)) {
                    setPreviewImage({ attachment, url });
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>
    );
  };
  
  const renderMessageBubble = (msg: SupportMessage, index: number) => {
    const isUserMessage = msg.sender === 'user';
    const isLastMessage = index === messages.length - 1;
    
    return (
      <div
        key={msg.id}
        className={`w-full flex ${isUserMessage ? 'justify-end' : 'justify-start'} mb-3`}
      >
        <div
          style={{
            maxWidth: '70%',
            padding: '10px 20px',
            borderRadius: isUserMessage 
              ? '20px 0 20px 20px' 
              : '0 20px 20px 20px',
            boxShadow: '0px 0px 10px 0px #00000010',
            backgroundColor: isUserMessage ? 'var(--accent)' : 'white',
            color: isUserMessage ? 'white' : 'black',
          }}
        >
          {/* Sender name for support messages */}
          {!isUserMessage && (
            <p className="text-xs font-semibold mb-1 text-gray-600">
              Assistenza Affitti
            </p>
          )}

          {/* Message text */}
          {msg.message && (
            <p className="mb-2" style={{ 
              color: isUserMessage ? 'white' : 'black',
              whiteSpace: 'pre-wrap'
            }}>
              {msg.message}
            </p>
          )}

          {/* Message timestamp */}
          <p className="text-xs mb-2" style={{
            color: isUserMessage ? 'rgba(255,255,255,0.7)' : '#999'
          }}>
            {new Date(msg.created_at).toLocaleString('it-IT', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>          {/* Message attachments */}
          {msg.attachments && msg.attachments.length > 0 && (
            <AttachmentList
              attachments={msg.attachments}
              sentAttachmentUrls={sentAttachmentUrls}
              isUserMessage={isUserMessage}
              layout={msg.attachments.length > 2 ? 'grid' : 'horizontal'}
              size="medium"              onAttachmentClick={(attachment, url) => {
                if (isImageFile(attachment.file_name)) {
                  setPreviewImage({ attachment, url });
                } else {
                  downloadFile(url, attachment.file_name);
                }
              }}
            />
          )}
        </div>
        {isLastMessage && <div ref={messagesEndRef} />}
      </div>
    );
  };
  // Show loading state while authentication is in progress
  if (isLoading) {
    return (
      <MobilePageStart noPadding>
        <HeaderPage
          actionLeftIcon={() => {
            router.back()
          }}
          title='Chat'
          actionRightIcon={() => {
            router.push('/dashboard')
          }}
        />
        <div className="flex flex-col items-center justify-center h-full">
          <Loader1 />
        </div>
      </MobilePageStart>
    )
  }

  // If not authenticated, show error message
  if (!isAuthenticated) {
    return (
      <MobilePageStart noPadding>
        <HeaderPage
          actionLeftIcon={() => {
            router.back()
          }}
          title='Chat'
          actionRightIcon={() => {
            router.push('/dashboard')
          }}
        />
      </MobilePageStart>
    )
  }

  return (
    <SupportChatErrorBoundary>
      <MobilePageStart
        noPadding
      >        <HeaderPage
          actionLeftIcon={() => {
            router.back()
          }}
          title='Chat'
          actionRightIcon={() => {
            router.push('/dashboard')
          }}
        />
        
        {/* Connection Status */}
        <div className="px-4 py-2 border-b border-gray-200">
          <ConnectionStatusIndicator 
            status={connectionStatus} 
            className="justify-center"
          />
        </div>
        <div
          style={
            {
              height: '100%',
              width: '100%',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              position: 'relative',
            }
          }
        >
          {/* Chat */}
          <div className='p-2'>
            <CardAlert
                title='Attenzione'
                message='I Messaggi inviati in questa chat saranno inoltrati via email al team di assistenza, che risponderà entro 48 ore'
                color='orange'
                style={{
                  marginBottom: '20px'
                }}
              />
          </div>
          
          <div className='flex flex-col w-full h-full overflow-y-auto'
            style={{
              paddingBottom: '70px',
              paddingLeft: '10px',
              paddingRight: '10px'
            }}
          >
            {/* Load more button */}
            {hasMore && (
              <div className="flex justify-center mb-4">
                <button
                  onClick={loadMore}
                  disabled={isLoadingMore}
                  className="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50"
                >
                  {isLoadingMore ? 'Caricamento...' : 'Carica altri messaggi'}
                </button>
              </div>
            )}            {/* Messages */}
            {messages.map((msg, index) => renderMessageBubble(msg, index))}
            
            {/* Pending Messages */}
            {pendingMessages.map(pendingMsg => renderPendingMessage(pendingMsg))}
            
            {/* Typing Indicator */}
            <TypingIndicator 
              typingStatus={typingStatus}
              className="mb-2"
            />
              {messages.length === 0 && !isInitializing && (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg className="w-16 h-16 mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
                <p className="text-center px-4">
                  Nessun messaggio ancora. Inizia una conversazione con il nostro team di supporto!
                </p>
              </div>
            )}
            
            {isInitializing && (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <Loader1 />
                <p>Caricamento messaggi...</p>
              </div>
            )}
          </div>          {/* Enhanced Message Input */}
          <div className="absolute bottom-0 left-0 right-0">
            <EnhancedMessageInput
              message={message}
              setMessage={setMessage}
              attachments={attachments}
              setAttachments={setAttachments}
              onSendMessage={handleSendMessage}
              onUserTyping={handleUserTyping}
              isConnected={connectionStatus.isConnected}
              disabled={isInitializing}
            />
          </div>
        </div>
      </MobilePageStart>
      
      {/* Image Preview Modal */}
      {previewImage && (
        <ImagePreviewModal
          attachment={previewImage.attachment}
          fileUrl={previewImage.url}
          isOpen={true}
          onClose={() => setPreviewImage(null)}
        />
      )}
    </SupportChatErrorBoundary>
  )
}

export default Page