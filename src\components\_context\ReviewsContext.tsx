"use client"

import React, { createContext, useState } from 'react';
import { getReviews } from '@/services/api';

interface ReviewsContextType {
    reviews: any[];
    isLoadingReviews: boolean;
    fetchReviews: () => Promise<void>;
}

export const ReviewsContext = createContext<ReviewsContextType>({
    reviews: [],
    isLoadingReviews: true,
    fetchReviews: async () => {},
});

export const ReviewsProvider = ({ children }: { children: React.ReactNode }) => {
    const [reviews, setReviews] = useState<any[]>([]);
    const [isLoadingReviews, setIsLoadingReviews] = useState<boolean>(false);

    const fetchReviews = async () => {
        setIsLoadingReviews(true);
        try {
            const data = await getReviews();
            setReviews(data);
        } catch (error) {
            console.error('Error fetching reviews:', error);
        }
        setIsLoadingReviews(false);
    };

    return (
        <ReviewsContext.Provider value={{ reviews, isLoadingReviews, fetchReviews }}>
            {children}
        </ReviewsContext.Provider>
    );
};
