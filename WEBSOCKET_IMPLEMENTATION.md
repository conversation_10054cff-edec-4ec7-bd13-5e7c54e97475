# WebSocket Implementation for Support Chat

## Overview
This document outlines the WebSocket implementation for the support chat feature. The WebSocket connection enables real-time communication between clients and the server, providing features such as instant messaging, typing indicators, and file upload progress tracking.

## Key Components

### 1. SupportChatWebSocket Class (src/services/SupportChatWebSocket.ts)
The primary class responsible for managing WebSocket connections, message tracking, reconnection logic, and event handling.

#### Features:
- **Auto Reconnection**: Implements exponential backoff strategy for reconnection attempts
- **Message Tracking**: Assigns client-side IDs to messages for tracking status
- **Message Retry**: Automatically retries failed messages with exponential backoff
- **Ping/Pong**: Maintains connection health with regular ping messages
- **Typing Indicators**: Sends real-time typing status updates
- **File Upload Progress**: Monitors and reports file upload progress
- **Error Handling**: Comprehensive error handling with specific error codes

### 2. useSupportChat Hook (src/hooks/useSupportChat.ts)
A React hook that provides a simplified interface for components to interact with the WebSocket functionality.

#### Features:
- **Message Management**: Handles sending, receiving, and tracking messages
- **Connection Status**: Exposes connection state for UI feedback
- **Typing Indicators**: Manages typing status for UI indicators
- **Message Confirmation**: Tracks and confirms message delivery
- **API Fallback**: Falls back to standard API if WebSocket is unavailable

### 3. FileUploadService Class (src/services/FileUploadService.ts)
Manages file uploads with progress tracking integrated with the WebSocket implementation.

#### Features:
- **Upload Progress**: Tracks and reports upload progress
- **WebSocket Integration**: Connects with WebSocket for real-time progress updates
- **Error Handling**: Comprehensive error handling for file uploads
- **Multiple File Support**: Manages multiple simultaneous uploads

## WebSocket Message Types

| Type | Direction | Description |
|------|-----------|-------------|
| `chat.message` | Bidirectional | Text messages sent between client and server |
| `ping` | Client → Server | Heartbeat message to keep connection alive |
| `pong` | Server → Client | Response to ping message |
| `typing.start` | Bidirectional | Indicates user has started typing |
| `typing.stop` | Bidirectional | Indicates user has stopped typing |
| `file.uploaded` | Server → Client | Notification that a file was uploaded successfully |
| `upload.progress` | Server → Client | Update on file upload progress |
| `client.disconnect` | Client → Server | Graceful disconnection notification |
| `error` | Server → Client | Error notification from server |

## WebSocket Close Codes

| Code | Description | Action |
|------|-------------|--------|
| 1000 | Normal closure | No action needed |
| 1001 | Going away | Attempt reconnection |
| 1006 | Abnormal closure | Attempt reconnection |
| 4001 | Authentication token missing | Prompt user to log in |
| 4002 | Invalid authentication token | Refresh token or prompt user to log in |
| 4003 | Token authentication error | Prompt user to log in |
| 4004 | Chat ID missing | Show error, verify navigation |
| 4005 | Chat not found | Show error, redirect to chats list |
| 4006 | Access denied to chat | Show permission error |
| 4007 | Error getting chat details | Show error, attempt to reload |

## Client Message Tracking
Each message sent by the client includes a unique `client_message_id` that allows for:
- Tracking delivery status (sending → sent → confirmed)
- Matching server responses to client requests
- UI feedback for message status
- Retrying failed messages

## Message Lifecycle

1. **Creation**: Client creates a message with a unique client-side ID
2. **Sending**: Message is sent via WebSocket and marked as "sending"
3. **Sent**: Message is successfully transmitted and marked as "sent"
4. **Confirmation**: Server processes message and sends confirmation with server-side ID
5. **Confirmed**: Client matches confirmation with pending message and marks as "confirmed"

## Implementation Improvements
- [x] Fixed WebSocket URL construction for HTTP/HTTPS protocols
- [x] Enhanced authentication and connection handling
- [x] Improved message sending and tracking with client-side IDs
- [x] Implemented robust reconnection logic with exponential backoff
- [x] Added ping/pong mechanism for connection monitoring
- [x] Enhanced typing indicator functionality
- [x] Added message confirmation status in UI
- [x] Implemented file upload progress tracking
- [x] Added comprehensive error handling

## Future Enhancements
- [ ] Implement offline message queueing with IndexedDB
- [ ] Add end-to-end encryption for messages
- [ ] Implement read receipts
- [ ] Add message reactions and threaded replies
- [ ] Implement presence indicators (online/offline status)
- [ ] Add support for voice and video messages

## References
- [WebSocket API Documentation](https://developer.mozilla.org/en-US/docs/Web/API/WebSocket)
- [RFC 6455 - The WebSocket Protocol](https://tools.ietf.org/html/rfc6455)
