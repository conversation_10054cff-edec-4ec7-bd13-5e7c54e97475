/* Input & Input Hidden */
.component {
  width: 100%;
}

.component p {
  font-size: 14px;
}

.component .countrySelect {
  transform: translateY(7px);
}

.inputContainer {
  width: 100%;
  height: 45px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: var(--card-background);
  -webkit-backdrop-filter: blur(30px);
          backdrop-filter: blur(30px);
  border-radius: 15px;
  padding: 0px 10px;
}

.inputContainerArea {
  width: 100%;
  height: 100px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: var(--card-background);
  -webkit-backdrop-filter: blur(30px);
          backdrop-filter: blur(30px);
  border-radius: 15px;
  padding: 15px 10px;
}

.inputContainer input {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  font-size: 13px;
  background: none;
  color: var(--text-faded);
  margin: 0;
  border: none;
  box-sizing: border-box;
}

.inputContainerArea textarea {
  width: 100%;
  height: 100%;
  border-radius: 5px;
  font-size: 13px;
  background: none;
  color: var(--text-faded);
  margin: 0;
  border: none;
  box-sizing: border-box;
}

.inputContainer input:focus {
  outline: none;
}

.inputContainerArea textarea:focus {
  outline: none;
}

.icon {
  height: 20px;
  width: 20px;
  color: var(--text-faded);
}

.error {
  height: 15px;
  margin-top: 5px;
  font-size: 11px;
  color: tomato;
  transform: translateY(-5px);
  text-align: end;
}

/* Input Search */
.container {
  width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.searchBox {
  width: 100%;
  height: 40px;
  background-color: var(--status-background);
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 20px;
  border-radius: 5px;
}
.searchBox .input {
  width: 100%;
  height: 100%;
  border: none;
  background: transparent;
}
.searchBox .input:focus {
  outline: none;
}
.searchBox .searchIcon {
  transition: opacity 0.2s, transform 0.3s;
}

.dropdown {
  margin-top: 5px;
  width: 100%;
  background-color: rgb(68, 68, 68);
  border-radius: 1rem;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

/* Input Select */
.selectComponent {
  width: 100%;
  margin-bottom: 15px;
}

.containerSelect {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: var(--card-background);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  font-weight: 300;
  color: var(--text-faded);
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
}
.containerSelect .dropdown {
  position: absolute;
  top: 30px;
  left: 0px;
  width: 100%;
  max-width: 100%;
  min-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  background-color: var(--card-background);
  color: var(--text-faded);
  border-radius: 0 0 1rem 1rem;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 2px;
  z-index: 10;
}
.containerSelect .dropdown .option {
  border-radius: 10px;
  min-height: 30px;
  padding: 0px 10px;
  display: flex;
  align-items: center;
  transition: all 0.3s;
}
.containerSelect .dropdown .option:hover {
  background-color: grey;
}

/* Input Edit */
.inputView {
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.inputEdit {
  width: 100%;
  height: 100%;
  background: none;
  border: none;
  text-align: center;
}

.inputEdit::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.inputEdit:focus {
  outline: none;
}

/* Input Code */
.componentCode {
  margin-bottom: 1rem;
}
.componentCode .inputCode {
  width: auto;
  border-radius: 5px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0px 10px;
  margin-top: 5px;
  position: relative;
}
.componentCode .inputCode .codeInput {
  width: 63px;
  height: 77px;
  border-radius: 8px;
  font-size: 30px;
  background-color: var(--card-background);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}
.componentCode .inputCode input {
  width: 100%;
  height: 100%;
  position: absolute;
  background: none;
  left: 0;
  border: none;
  font-size: 13px;
  color: rgba(0, 0, 0, 0);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.componentCode .inputCode input[type=number]::-webkit-inner-spin-button,
.componentCode .inputCode input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.componentCode .inputCode input:focus {
  outline: none;
}

/* Input Counter */
.inputCounter button {
  width: 40px;
  height: 40px;
  background-color: var(--accent);
  border-radius: 10px;
}/*# sourceMappingURL=input.module.css.map */