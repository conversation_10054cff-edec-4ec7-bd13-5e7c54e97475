'use client'
import React, { Suspense, useEffect, useState } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import Button from '@/components/buttons/Button'

import { useRouter, useSearchParams } from 'next/navigation'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'

import InputCalendar from '@/components/inputs/InputCalendar'
import {  createRoomRate, getPropertyRates,  roomsListAllRoomsProperty, viewAllRatePlans } from '@/services/api'
import toast from 'react-hot-toast'
import InputCounter from '@/components/inputs/inputCounter'
import LoaderWhite from '@/components/loaders/LoaderWhite'
import { useCalendar } from "@/components/_context/CalendarContext";


export default function Main() {
  return (
    <Suspense fallback={<LoaderWhite />}>
      <Page />
    </Suspense>
  )
}

function Page() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { clearCache } = useCalendar();
  //States
  const startDate = searchParams.get('start_date')
  const endDate = searchParams.get('end_date')
  const property = searchParams.get('property')

  const [selectedStartDate, setSelectedStartDate] = useState(startDate ?? null)
  const [selectedEndDate, setSelectedEndDate] = useState(endDate ?? null)
  const [propertyRates, setPropertyRates] = useState<any[]>([])
  const [selcetedRate, setSelectedRate] = useState<any>(null)

  const [roomRate, setRoomRate] = useState<number>(0)
  const [minNights, setMinNights] = useState<number>(1)
  const [maxNights, setMaxNights] = useState<number>(30)
  const [roomRates, setRoomRates] = useState([])


  const handleCreateRoomRate = async () => {
    if (!property) {
      console.error('No property selected')
      toast.error('Parametry query mancanti o incorretti')
      return
    }

    if (!selectedStartDate) {
      console.error('No start date selected')
      toast.error('Nessuna data di inizio selezionata')
      return
    }

    if (!selectedEndDate) {
      console.error('No end date selected')
      toast.error('Nessuna data di fine selezionata')
      return
    }

     //Check if checkin date is prior to today
     if (new Date(new Date(selectedStartDate).setHours(23, 59, 59, 0)) < new Date()) {
      toast.error('La data di checkin non puo essere antecedente ad oggi')
      return
    }

    if (new Date(new Date(selectedStartDate).setHours(23, 59, 59, 0)) > new Date(selectedEndDate)) {
      toast.error('La data di checkout non puo essere antecedente alla data di checkout')
      return
    }

    if (!roomRate) {
      console.error('No room rate selected')
      toast.error('Nessun prezzo selezionato')
      return
    }

    if (!minNights) {
      console.error('No min nights selected')
      toast.error('Nessun minimo di notti selezionato')
      return
    }

    if (!maxNights) {
      console.error('No max nights selected')
      toast.error('Nessun massimo di notti selezionato')
      return
    }

    if (minNights > maxNights) {
      console.error('Min nights is greater than max nights')
      toast.error('Il minimo di notti è maggiore del massimo di notti')
      return
    }

    if (!selcetedRate) {
      console.error('No rate selected')
      toast.error('Nessuna tariffa selezionata')
      return
    }

    const propertyData = await roomsListAllRoomsProperty(property)

    const call = await createRoomRate(
      propertyData[0].id,
      selcetedRate,
      roomRate.toString(),
      selectedStartDate,
      selectedEndDate,
      minNights,
      maxNights
    )

    if (call.status === 200 || call.status == 201){
      clearCache(); // Clear the calendar cache
      toast.success('Tariffa creata correttamente')
      setTimeout(() => {
        router.push("/dashboard/calendar")
      }, 1000)
    } else if (call.status === 400){
      toast.error('Errore durante la creazione della stagione')
    }

  }
  const handleFetchPropertyRates = async () => {
    if (!property) return

    const call = await viewAllRatePlans(property)
    setPropertyRates(call)
    setSelectedRate(call[0].id)

  }
  const handleFetchRoomRates = async () => {
    if (!property) {
      console.error('Parametry query mancanti o incorretti')
      toast.error
      return
    }

  const call = await getPropertyRates(property)
    
    if (call.length > 0){
      setMinNights(call[0].minimum_stay)
      setMaxNights(call[0].maximum_stay)
      setRoomRate(call[0].rate)
    }
  }

  useEffect(() => {
    handleFetchRoomRates()
    handleFetchPropertyRates()
  }, [])
  return (
    <MobilePageStart>
      <HeaderPage
        title='Prezzi proprieta'
        actionLeftIcon={
          () => {
            router.back()
          }
        }
        actionRightIcon={
          () => {
            router.back()
          }
        }
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col justify-between'>

        <div className='flex flex-col gap-4'>

          <InputCalendar
            label='Data di inizio'
            value={selectedStartDate ?? new Date().toISOString().split('T')[0]}
            onChange={(value) => {
              setSelectedStartDate(value)
            }}
          />

          <InputCalendar
            label='Data di fine'
            value={selectedEndDate ?? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
            onChange={(value) => {
              setSelectedEndDate(value)
            }}
          />

          {/* <InputSelectLabel
            label='Tariffa'
            value={
              propertyRates.find((rate) => rate.id === selcetedRate)?.name ?? ''
            }
            placeholder='Seleziona una proprietà'
            onSelect={(value) => {
              setSelectedRate(value)
            }}
            options={propertyRates.map((property) => {
              return {
          value: property.id,
          label: property.name
              }
            })}
          /> */}

          <InputLabelDescription
            label='Prezzo a notte configurato in Heibooky (€)'
            value={roomRate.toString()}
            onChange={(value) => {
              setRoomRate(value)
            }}
            type='number'
          />
          <div className='flex flex-row justify-between text-[12px]'>
            <p>Utile netto previsto (potrebbe essere applicata l'IVA).</p>
            <p className='text-red-700'> ~ {(roomRate * 0.73).toFixed(2)}€</p>
          </div>

          <InputCounter
            label='Numero minimo di notti'
            value={minNights}
            onChange={setMinNights}
          />
          <InputCounter
            label='Massimo di notti'
            value={maxNights}
            onChange={setMaxNights}
          />
        </div>

        <Button
          color='white'
          backgroundColor='var(--blue)'
          text='Crea Tariffa'
          fontSize='14px'
          onClick={() => {
            handleCreateRoomRate()
          }}
        />
      </div>
    </MobilePageStart>
  )
}

