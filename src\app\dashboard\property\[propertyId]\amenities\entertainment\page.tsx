'use client'

import HeaderPage from '@/components/_globals/headerPage'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import InputOnOff from '@/components/inputs/inputOnOff'
import Loader1 from '@/components/loaders/Loader1'
import { amenitiesCreateAndUpdate, amenitiesPropertyAmenities } from '@/services/api'
import { useRouter } from 'next/navigation'

import React, { useEffect, useState } from 'react'

interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({ params }: PageProps) {
  const router = useRouter()

  const [isLoading, setIsLoading] = useState(true)
  const [data, setData] = React.useState({
    cabletv: false,
    satellitetv: false,
    streaming: false,
    dvd: false,
    cd: false,
    radio: false,
    speakers: false,
    gameconsole: false,
    boardgames: false,
    pingpong: false,
    pool: false,
    running: false,
    biking: false,
    mountainbiking: false,
    hiking: false,
    ski: false,
    climbing: false,
    paragliding: false,
    golf: false,
    paddle: false,
    surf: false,
    windsurf: false,
    kitesurf: false,
    wingfoil: false,
    swimming: false,
    sailing: false,
    diving: false,
    canoeing: false,
    canioning: false,
    watersports: false,
    fishing: false,
    horseriding: false,
    tennis: false,
    yoga: false,
  })

  const handleUpdateAmenities = async (data: any) => {
    if (!params.propertyId) return;

    const dataAmenities = [
      { name: 'CabletV', category: 'Entertainment', is_available: data.cabletv ?? false },
      { name: 'Satellitetv', category: 'Entertainment', is_available: data.satellitetv ?? false },
      { name: 'Streaming', category: 'Entertainment', is_available: data.streaming ?? false },
      { name: 'Dvd', category: 'Entertainment', is_available: data.dvd ?? false },
      { name: 'Cd', category: 'Entertainment', is_available: data.cd ?? false },
      { name: 'Radio', category: 'Entertainment', is_available: data.radio ?? false },
      { name: 'Speakers', category: 'Entertainment', is_available: data.speakers ?? false },
      { name: 'Gameconsole', category: 'Entertainment', is_available: data.gameconsole ?? false },
      { name: 'Boardgames', category: 'Entertainment', is_available: data.boardgames ?? false },
      { name: 'Pingpong', category: 'Entertainment', is_available: data.pingpong ?? false },
      { name: 'Pool', category: 'Entertainment', is_available: data.pool ?? false },

      { name: 'Running', category: 'Sports & Outdoors', is_available: data.running ?? false },
      { name: 'Biking', category: 'Sports & Outdoors', is_available: data.biking ?? false },
      { name: 'Mountainbiking', category: 'Sports & Outdoors', is_available: data.mountainbiking ?? false },
      { name: 'Hiking', category: 'Sports & Outdoors', is_available: data.hiking ?? false },
      { name: 'Ski', category: 'Sports & Outdoors', is_available: data.ski ?? false },
      { name: 'Climbing', category: 'Sports & Outdoors', is_available: data.climbing ?? false },
      { name: 'Paragliding', category: 'Sports & Outdoors', is_available: data.paragliding ?? false },
      { name: 'Golf', category: 'Sports & Outdoors', is_available: data.golf ?? false },
      { name: 'Paddle', category: 'Sports & Outdoors', is_available: data.paddle ?? false },
      { name: 'Surf', category: 'Sports & Outdoors', is_available: data.surf ?? false },
      { name: 'Windsurf', category: 'Sports & Outdoors', is_available: data.windsurf ?? false },
      { name: 'Kitesurf', category: 'Sports & Outdoors', is_available: data.kitesurf ?? false },
      { name: 'Wingfoil', category: 'Sports & Outdoors', is_available: data.wingfoil ?? false },
      { name: 'Swimming', category: 'Sports & Outdoors', is_available: data.swimming ?? false },
      { name: 'Sailing', category: 'Sports & Outdoors', is_available: data.sailing ?? false },
      { name: 'Diving', category: 'Sports & Outdoors', is_available: data.diving ?? false },
      { name: 'Canoeing', category: 'Sports & Outdoors', is_available: data.canoeing ?? false },
      { name: 'Canioning', category: 'Sports & Outdoors', is_available: data.canioning ?? false },
      { name: 'Watersports', category: 'Sports & Outdoors', is_available: data.watersports ?? false },
      { name: 'Fishing', category: 'Sports & Outdoors', is_available: data.fishing ?? false },
      { name: 'Horseriding', category: 'Sports & Outdoors', is_available: data.horseriding ?? false },
      { name: 'Tennis', category: 'Sports & Outdoors', is_available: data.tennis ?? false },
      { name: 'Yoga', category: 'Sports & Outdoors', is_available: data.yoga ?? false },
    ];

    const call = await amenitiesCreateAndUpdate(params.propertyId, dataAmenities);

  };

  const handleFetchAmenities = async () => {
    if (!params.propertyId) return

     const call = await amenitiesPropertyAmenities(params.propertyId) as any

        if (call.status === 400){
          setIsLoading(false)
          return
        }
    
    
    
        
        // Update the data state based on the response
        const fetchedData = call.data.amenities.reduce((acc: any, amenity: any) => {
      const key = amenity.name
        .toLowerCase() // Convert the string to lowercase
        .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match: any, index: number) => index === 0 ? match.toLowerCase() : match.toUpperCase()) // Convert to camelCase
        .replace(/\s+/g, ''); // Remove spaces

      acc[key] = amenity.is_available;
      return acc;
    }, {});


    setData(fetchedData);
    setIsLoading(false)
  }

  useEffect(() => {
    handleFetchAmenities()
  }, [])

  return (
    <MobilePageStart isNavbar={false}>
      <HeaderPage
        title='Intrattenimento'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />


      {
        isLoading ?
          <div className='h-full w-full flex items-center justify-center'>
            <Loader1 />
          </div>
          :
          <div className='mt-4 px-4 h-full w-full flex flex-col gap-3'>
            <InputOnOff
              title='TV via cavo'
              value={data.cabletv}
              onChange={(value) => {
                setData({
                  ...data,
                  cabletv: value
                })
                handleUpdateAmenities({
                  ...data,
                  cabletv: value
                })
              }}
            />

            <InputOnOff
              title='TV satellitare'
              value={data.satellitetv}
              onChange={(value) => {
                setData({
                  ...data,
                  satellitetv: value
                })
                handleUpdateAmenities({
                  ...data,
                  satellitetv: value
                })
              }}
            />

            <InputOnOff
              title='Streaming Video'
              description='Netflix, Amazon Prime, Disney+'
              value={data.streaming}
              onChange={(value) => {
                setData({
                  ...data,
                  streaming: value
                })
                handleUpdateAmenities({
                  ...data,
                  streaming: value
                })
              }}
            />

            <InputOnOff
              title='Lettore DVD'
              value={data.dvd}
              onChange={(value) => {
                setData({
                  ...data,
                  dvd: value
                })
                handleUpdateAmenities({
                  ...data,
                  dvd: value
                })
              }}
            />

            <InputOnOff
              title='Lettore CD'
              value={data.cd}
              onChange={(value) => {
                setData({
                  ...data,
                  cd: value
                })
                handleUpdateAmenities({
                  ...data,
                  cd: value
                })
              }}
            />

            <InputOnOff
              title='Radio'
              value={data.radio}
              onChange={(value) => {
                setData({
                  ...data,
                  radio: value
                })
                handleUpdateAmenities({
                  ...data,
                  radio: value
                })
              }}
            />

            <InputOnOff
              title='Casse Audio'
              value={data.speakers}
              onChange={(value) => {
                setData({
                  ...data,
                  speakers: value
                })
                handleUpdateAmenities({
                  ...data,
                  speakers: value
                })
              }}
            />

            <InputOnOff
              title='Console Videogiochi'
              value={data.gameconsole}
              onChange={(value) => {
                setData({
                  ...data,
                  gameconsole: value
                })
                handleUpdateAmenities({
                  ...data,
                  gameconsole: value
                })
              }}
            />

            <InputOnOff
              title='Giochi da Tavolo'
              value={data.boardgames}
              onChange={(value) => {
                setData({
                  ...data,
                  boardgames: value
                })
                handleUpdateAmenities({
                  ...data,
                  boardgames: value
                })
              }}
            />



            <InputOnOff
              title='Ping Pong'
              value={data.pingpong}
              onChange={(value) => {
                setData({
                  ...data,
                  pingpong: value
                })
                handleUpdateAmenities({
                  ...data,
                  pingpong: value
                })
              }}
            />


            <InputOnOff
              title='Biliardo'
              value={data.pool}
              onChange={(value) => {
                setData({
                  ...data,
                  pool: value
                })
                handleUpdateAmenities({
                  ...data,
                  pool: value
                })
              }}
            />

            <InputOnOff
              title='Corsa'
              value={data.running}
              onChange={(value) => {
                setData({
                  ...data,
                  running: value
                })
                handleUpdateAmenities({
                  ...data,
                  running: value
                })
              }}
            />

            <InputOnOff
              title='Bicicletta'
              value={data.biking}
              onChange={(value) => {
                setData({
                  ...data,
                  biking: value
                })
                handleUpdateAmenities({
                  ...data,
                  biking: value
                })
              }}
            />

            <InputOnOff
              title='Mountainbike'
              value={data.mountainbiking}
              onChange={(value) => {
                setData({
                  ...data,
                  mountainbiking: value
                })
                handleUpdateAmenities({
                  ...data,
                  mountainbiking: value
                })
              }}
            />

            <InputOnOff
              title='Trekking'
              value={data.hiking}
              onChange={(value) => {
                setData({
                  ...data,
                  hiking: value
                })
                handleUpdateAmenities({
                  ...data,
                  hiking: value
                })
              }}
            />

            <InputOnOff
              title='Sci'
              value={data.ski}
              onChange={(value) => {
                setData({
                  ...data,
                  ski: value
                })
                handleUpdateAmenities({
                  ...data,
                  ski: value
                })
              }}
            />

            <InputOnOff
              title='Scalata'
              value={data.climbing}
              onChange={(value) => {
                setData({
                  ...data,
                  climbing: value
                })
                handleUpdateAmenities({
                  ...data,
                  climbing: value
                })
              }}
            />

            <InputOnOff
              title='Parapendio'
              value={data.paragliding}
              onChange={(value) => {
                setData({
                  ...data,
                  paragliding: value
                })
                handleUpdateAmenities({
                  ...data,
                  paragliding: value
                })
              }}
            />

            <InputOnOff
              title='Golf'
              value={data.golf}
              onChange={(value) => {
                setData({
                  ...data,
                  golf: value
                })
                handleUpdateAmenities({
                  ...data,
                  golf: value
                })
              }}
            />

            <InputOnOff
              title='Paddle'
              value={data.paddle}
              onChange={(value) => {
                setData({
                  ...data,
                  paddle: value
                })
                handleUpdateAmenities({
                  ...data,
                  paddle: value
                })
              }}
            />

            <InputOnOff
              title='Surf'
              value={data.surf}
              onChange={(value) => {
                setData({
                  ...data,
                  surf: value
                })
                handleUpdateAmenities({
                  ...data,
                  surf: value
                })
              }}
            />

            <InputOnOff
              title='Windsurf'
              value={data.windsurf}
              onChange={(value) => {
                setData({
                  ...data,
                  windsurf: value
                })
                handleUpdateAmenities({
                  ...data,
                  windsurf: value
                })
              }}
            />

            <InputOnOff
              title='Kitesurf'
              value={data.kitesurf}
              onChange={(value) => {
                setData({
                  ...data,
                  kitesurf: value
                })
                handleUpdateAmenities({
                  ...data,
                  kitesurf: value
                })
              }}
            />

            <InputOnOff
              title='Wing Foil'
              value={data.wingfoil}
              onChange={(value) => {
                setData({
                  ...data,
                  wingfoil: value
                })
                handleUpdateAmenities({
                  ...data,
                  wingfoil: value
                })
              }}
            />

            <InputOnOff
              title='Nuoto'
              value={data.swimming}
              onChange={(value) => {
                setData({
                  ...data,
                  swimming: value
                })
                handleUpdateAmenities({
                  ...data,
                  swimming: value
                })
              }}
            />

            <InputOnOff
              title='Vela'
              value={data.sailing}
              onChange={(value) => {
                setData({
                  ...data,
                  sailing: value
                })
                handleUpdateAmenities({
                  ...data,
                  sailing: value
                })
              }}
            />

            <InputOnOff
              title='Immersioni'
              value={data.diving}
              onChange={(value) => {
                setData({
                  ...data,
                  diving: value
                })
                handleUpdateAmenities({
                  ...data,
                  diving: value
                })
              }}
            />

            <InputOnOff
              title='Canoa/Kayak'
              value={data.canoeing}
              onChange={(value) => {
                setData({
                  ...data,
                  canoeing: value
                })
                handleUpdateAmenities({
                  ...data,
                  canoeing: value
                })
              }}
            />

            <InputOnOff
              title='Canioning'
              value={data.canioning}
              onChange={(value) => {
                setData({
                  ...data,
                  canioning: value
                })
                handleUpdateAmenities({
                  ...data,
                  canioning: value
                })
              }}
            />

            <InputOnOff
              title='Giochi Acquatici'
              value={data.watersports}
              onChange={(value) => {
                setData({
                  ...data,
                  watersports: value
                })
                handleUpdateAmenities({
                  ...data,
                  watersports: value
                })
              }}
            />

            <InputOnOff
              title='Pesca'
              value={data.fishing}
              onChange={(value) => {
                setData({
                  ...data,
                  fishing: value
                })
                handleUpdateAmenities({
                  ...data,
                  fishing: value
                })
              }}
            />

            <InputOnOff
              title='Equitazione'
              value={data.horseriding}
              onChange={(value) => {
                setData({
                  ...data,
                  horseriding: value
                })
                handleUpdateAmenities({
                  ...data,
                  horseriding: value
                })
              }}
            />

            <InputOnOff
              title='Tennis'
              value={data.tennis}
              onChange={(value) => {
                setData({
                  ...data,
                  tennis: value
                })
                handleUpdateAmenities({
                  ...data,
                  tennis: value
                })
              }}
            />

            <InputOnOff
              title='Yoga'
              value={data.yoga}
              onChange={(value) => {
                setData({
                  ...data,
                  yoga: value
                })
                handleUpdateAmenities({
                  ...data,
                  yoga: value
                })
              }}
            />

          </div>
      }


    </MobilePageStart>
  )
}

export default Page



