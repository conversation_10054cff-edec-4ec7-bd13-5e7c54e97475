'use client'
import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import PhoneInput from 'react-phone-number-input'
import { isValidPhoneNumber } from 'react-phone-number-input'
import 'react-phone-number-input/style.css'

// Components
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import InputLabelDescription from '@/components/inputs/inputLabelDescription'
import Button from '@/components/buttons/Button'
import Loader1 from '@/components/loaders/Loader1'

// API services
import { 
  arrivalCreateGuestArrival, 
  arrivalUpdateGuestArrival, 
  arrivalViewGuestArrival 
} from '@/services/api'

interface PageProps {
  params: {
    propertyId: string
  }
}

function Page({ params }: PageProps) {
  const router = useRouter()
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    surname: '',
    email: '',
    phone: ''  // This will now store the full phone number in E.164 format
  })
  
  // Error state
  const [errors, setErrors] = useState({
    name: '',
    surname: '',
    email: '',
    phone: ''
  })
  
  // Component state
  const [arrivalsId, setArrivalsId] = useState<string | null>(null)
  const [isNew, setIsNew] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  // Handle input changes
  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user types
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }
  
  // Handle phone input change
  const handlePhoneChange = (value: string | undefined) => {
    setFormData(prev => ({ ...prev, phone: value || '' }))
    // Clear error when user types
    if (errors.phone) {
      setErrors(prev => ({ ...prev, phone: '' }))
    }
  }
  
  // Validation functions
  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }
  
  const validateForm = () => {
    const newErrors = {
      name: '',
      surname: '',
      email: '',
      phone: ''
    }
    
    let isValid = true
    
    if (!formData.name.trim()) {
      newErrors.name = 'Il nome è obbligatorio'
      isValid = false
    }
    
    if (!formData.surname.trim()) {
      newErrors.surname = 'Il cognome è obbligatorio'
      isValid = false
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'L\'email è obbligatoria'
      isValid = false
    } else if (!validateEmail(formData.email)) {
      newErrors.email = 'Formato email non valido'
      isValid = false
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Il numero di telefono è obbligatorio'
      isValid = false
    } else if (!isValidPhoneNumber(formData.phone)) {
      newErrors.phone = 'Formato numero di telefono non valido'
      isValid = false
    }
    
    setErrors(newErrors)
    return isValid
  }
  
  // Fetch existing arrival data
  const handleFetchArrivalData = async () => {
    try {
      const call = await arrivalViewGuestArrival(params.propertyId)
      
      if (call.error) {
        setIsNew(true)
      } else {
        // Set the phone number directly as it comes from the backend in E.164 format
        setFormData({
          name: call.contact_name || '',
          surname: call.contact_surname || '',
          email: call.email || '',
          phone: call.phone_number || ''  // This should already be in E.164 format like "+390226830102"
        })
        setArrivalsId(call.id)
      }
    } catch (error) {
      console.error('Error fetching arrival data:', error)
      toast.error('Errore nel caricamento dei dati')
      setIsNew(true)
    } finally {
      setIsLoading(false)
    }
  }
  
  // Update existing arrival data
  const handleUpdateArrivalData = async () => {
    if (!arrivalsId) {
      toast.error('ID arrivo mancante')
      return
    }
    
    setIsSubmitting(true)
    
    try {
      const { name, surname, email, phone } = formData
      // phone is already in E.164 format from the PhoneInput component
      const call = await arrivalUpdateGuestArrival(arrivalsId, params.propertyId, name, surname, email, phone)

      if (!call.error) {
        toast.success('Dati salvati con successo')
        router.back()
      } else {
        toast.error(call?.message || 'Errore durante l\'aggiornamento')
      }
    } catch (error) {
      console.error('Error updating arrival data:', error)
      toast.error('Errore durante l\'aggiornamento dei dati')
    } finally {
      setIsSubmitting(false)
    }
  }
  
  // Create new arrival data
  const handleCreateArrivalData = async () => {
    setIsSubmitting(true)
    
    try {
      const { name, surname, email, phone } = formData
      // phone is already in E.164 format from the PhoneInput component
      const call = await arrivalCreateGuestArrival(params.propertyId, name, surname, email, phone)
      
      if (!call.error) {
        toast.success('Dati salvati con successo')
        router.back()

      } else if (call.error && call.status === 400) {
        toast.error(call.message || 'Errore durante la creazione')
      } else {
        toast.error('Errore durante la creazione')
        return false
      }
    } catch (error) {
      toast.error('Si è verificato un errore')
      return false
    }
  }
  // Handle form submission
  const handleSubmit = () => {
    if (!validateForm()) {
      toast.error('Correggi gli errori nel form')
      return
    }
    
    isNew ? handleCreateArrivalData() : handleUpdateArrivalData()
  }
  
  // Initial data fetch
  useEffect(() => {
    handleFetchArrivalData()
  }, [])
  
  return (
    <MobilePageStart>
      <HeaderPage 
        title='Arrivo ospiti'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />
      
      {isLoading ? (
        <div className='h-full w-full flex items-center justify-center'>
          <Loader1 />
        </div>
      ) : (
        <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>
          <div className='flex flex-col gap-4'>
            <InputLabelDescription
              label='Nome'
              description='Questo campo sarà visibile sulla piattaforma, e tutti i canali di distribuzione.'
              placeholder='Inserire Nome'
              value={formData.name}
              onChange={(value) => handleChange('name', value)}
              error={errors.name}
              required
            />
            
            <InputLabelDescription
              label='Cognome'
              description='Questo campo sarà visibile sulla piattaforma, e tutti i canali di distribuzione.'
              placeholder='Inserire Cognome'
              value={formData.surname}
              onChange={(value) => handleChange('surname', value)}
              error={errors.surname}
              required
            />
            
            <InputLabelDescription
              label='Email'
              description='Inserisci un indirizzo email valido per le comunicazioni.'
              placeholder='Inserire email'
              value={formData.email}
              onChange={(value) => handleChange('email', value)}
              error={errors.email}
              required
            />
            
            <div className='p-4 bg-white rounded-lg'>
              <div className="mb-2 font-medium">Numero di telefono <span className="text-red-500">*</span></div>
              <div className="text-sm text-gray-500 mb-2">Inserisci un numero di telefono valido per le comunicazioni.</div>
              <PhoneInput
                international
                defaultCountry="IT"
                value={formData.phone}
                onChange={handlePhoneChange}
                onBlur={() => {
                  if (formData.phone && !isValidPhoneNumber(formData.phone)) {
                    setErrors(prev => ({ ...prev, phone: 'Formato numero di telefono non valido' }))
                  }
                }}
                className="w-full"
              />
              {errors.phone && <p className="text-red-500 text-sm mt-1">{errors.phone}</p>}
            </div>
          </div>
          
          <Button
            color='white'
            backgroundColor='var(--blue)'
            text={isSubmitting ? 'Caricamento...' : 'Continua'}
            fontSize='14px'
            onClick={handleSubmit}
            disabled={isSubmitting}
          />
          
          <br />
        </div>
      )}
    </MobilePageStart>
  )
}

export default Page