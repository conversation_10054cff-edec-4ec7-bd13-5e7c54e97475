'use client'
import React, { useEffect, useState, useContext } from 'react'
import MobilePageStart from '@/components/_globals/mobilePageStart'
import HeaderPage from '@/components/_globals/headerPage'
import { useRouter } from 'next/navigation'
import toast from 'react-hot-toast'
import { getBilling, signContract } from '@/services/api'
import ButtonLoading from '@/components/buttons/ButtonLoading'
import { UserContext } from '@/components/_context/UserContext'
import { CheckCircle } from 'lucide-react'


function Page() {
  const router = useRouter()
  const { hasBillingProfile, isCustomer } = useContext(UserContext)
  const isContractSigned = hasBillingProfile && isCustomer

  const handleAcceptContract = async () => {
    const call = await signContract()

    if (call.status === 200 || call.status === 201){
      toast.success('Contratto firmato con successo')
      // Refresh the user profile to update the context
      router.push("/dashboard/settings/account/")
    } else {
      toast.error("Assicurati che le tue informazioni di fatturazione siano corrette e contatta l'assistenza per ricevere assistenza.")
    }

    return true
  }

  return (
    <MobilePageStart>
      <HeaderPage title='Contratto'
        actionLeftIcon={() => {
          router.back()
        }}
        actionRightIcon={() => {
          router.push(`/dashboard`)
        }}
      />

      <div className='mt-4 px-4 h-full w-full flex flex-col gap-6'>


        <div className='flex flex-col gap-10'>
          <div>
            <h3>Sezione 1</h3>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
          </div>
          <div>
            <h3>Sezione 2</h3>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
          </div>
          <div>
            <h3>Sezione 3</h3>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
            <li>Lorem ipsum dolor sit amet consectetur adipisicing elit. Quae odit beatae consectetur rem id! Facilis quo, sunt eveniet eius ipsa expedita distinctio quam neque beatae praesentium alias, minus unde a.</li>
          </div>
        </div>



        {isContractSigned ? (
          <div className="mt-20 flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center mb-4">
              <CheckCircle size={32} className="text-green-500" />
            </div>
            <h3 className="text-lg font-semibold text-center mb-2">Contratto Firmato</h3>
            <p className="text-[13px] text-center text-gray-600">
              Hai già firmato il contratto. Non sono necessarie ulteriori azioni.
            </p>
          </div>
        ) : (
          <>
            <p className='text-[13px] text-center mt-20'>Cliccando 'Accetto' confermi di aver letto il contratto e accettarne tutti i termini e condizioni</p>
            <ButtonLoading
              color='white'
              backgroundColor='var(--blue)'
              text='Accetto & Continua'
              onClick={() => {
                return handleAcceptContract()
              }}
            />
          </>
        )}
        <br />
      </div>


    </MobilePageStart>
  )
}

export default Page