import React from 'react'
import styles from './globals.module.css'



/*
  @MobilePageStart
  @desc
    This component is used to create a mobile page start layout
    It is used to wrap the page content and add the necessary padding
    It is used to create a mobile page start layout
    It is used to wrap the page content and add the necessary padding
  @props
    children: React.ReactNode
    isNavbar?: boolean -> This is used to determine if the page has a navbar so to add or remove padding
*/
interface MobilePageProps {
  children: React.ReactNode
  isNavbar?: boolean
  noPadding?: boolean
  customPaddingBottom?: string
}

function MobilePageStart({ children, isNavbar, noPadding, customPaddingBottom }: MobilePageProps) {
  return (
    <div
      className={styles.mobilePageStart}
      style={{
        paddingBottom: customPaddingBottom ? customPaddingBottom : isNavbar ? 'max(90px, calc(70px + env(safe-area-inset-bottom)))' : noPadding ? '0' : '70px',
      }}
    >
      {children}
    </div>
  )
}

export default MobilePageStart