import React from 'react';
import { format } from 'date-fns';

interface CardReviewProps {
    publicReview: string;
    submittedAt: string;
    reviewerRole: string;
}

const CardReview: React.FC<CardReviewProps> = ({
    publicReview,
    submittedAt,
    reviewerRole
}) => {
    return (
        <div className="w-full p-4 bg-white rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-semibold capitalize text-blue-600">
                    {reviewerRole}
                </span>
                <span className="text-xs text-gray-500">
                    {format(new Date(submittedAt), 'dd MMM yyyy')}
                </span>
            </div>
            <p className="text-sm text-gray-700">{publicReview}</p>
        </div>
    );
};

export default CardReview;
